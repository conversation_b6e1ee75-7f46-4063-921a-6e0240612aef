# Bundle Tool 分层化配置管理和主包构建流程优化实现总结

## 概述

本次开发完成了 Bundle Tool 的重大升级，实现了分层化配置管理、界面交互优化和主包构建流程的智能化改进。所有开发工作都在理解并延续现有逻辑的基础上进行，严格遵守项目的 ESLint 语法规范。

## 主要功能实现

### 1. 分层化配置管理

#### 1.1 配置文件结构设计

**主配置文件 (config.yaml)**
```yaml
# Cocos Creator 热更新插件主配置文件
# 定义可供选择的项目列表

# 项目列表配置
projects:
  - pkw-game
  - wpk-game
```

**项目独立配置文件 (pkw-game.yaml, wpk-game.yaml)**
```yaml
# PKW Game 项目配置文件
# 包含主包更新和Bundle更新的完整配置

# 主包更新配置
main_package:
  version: "1.0.0"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.pkw-game.com/h5/"
      # ...
  export_directory: "./build/pkw-game/main-package"

# Bundle更新配置
bundle_update:
  export_directory: "./build/pkw-game/bundles"
  server_addresses:
    # ...
  bundles:
    common:
      version: "1.0"
      include: true  # 新增 include 字段
      dependencies: []
    # ...
```

#### 1.2 ConfigManager 重构

**新增接口定义**
- `IMainConfig`: 主配置文件结构
- `IProjectConfig`: 项目配置文件结构
- 保留 `IYamlConfig` 用于向后兼容

**核心方法**
- `getProjectList()`: 获取项目列表
- `setCurrentProject(projectName)`: 设置当前项目
- `getServerAddress(updateType, env, platform, projectName?)`: 获取服务器地址
- `getExportDirectory(updateType, projectName?)`: 获取导出目录
- `getAllBundleConfigs(projectName?)`: 获取所有 Bundle 配置

### 2. 界面交互优化

#### 2.1 新增 Project 选择器

**HTML 模板更新**
```html
<ui-prop name="Project">
    <div class="flex-1 layout horizontal center">
        <ui-select @confirm="onProjectSelectChange" v-bind:value="selectedProject">
            <option v-for="project in projectOptions" :key="project" :label="project"
                :value="project">
                {{project}}
            </option>
        </ui-select>
    </div>
</ui-prop>
```

#### 2.2 动态加载和联动刷新

**项目切换联动逻辑**
- 项目选择变更时自动加载对应的项目配置
- 更新导出目录路径
- 更新服务器地址配置
- 重新加载 Bundle 列表
- 同步 Include 状态

**数据绑定**
- `selectedProject`: 当前选择的项目
- `projectOptions`: 可选项目列表
- Bundle 列表中的 Include 复选框与配置文件中的 include 字段双向绑定

### 3. 主包构建流程优化

#### 3.1 main_package_generator.js 脚本增强

**新增参数支持**
```bash
node main_package_generator.js --included-bundles '["common","lobby-common"]'
```

**条件化打包逻辑**
- 根据 `includedBundles` 参数决定是否包含特定 Bundle
- 主包资源目录 (`internal`, `main`, `resources`) 始终包含
- Bundle 目录根据 include 状态条件化包含
- 详细的日志记录处理过程

#### 3.2 文件复制优化

**智能文件复制**
- 复制 `src` 目录（完整复制）
- 复制主包资源目录
- 根据项目配置条件化复制 Bundle 目录
- 跳过未包含的 Bundle，减少文件体积

**复制逻辑示例**
```typescript
// 检查是否为主包资源目录
if (['internal', 'main', 'resources'].includes(item)) {
    // 始终复制主包资源
    this.copyDirectoryRecursive(itemPath, targetPath);
} else if (includedBundles.includes(item)) {
    // 复制包含的 Bundle
    this.copyDirectoryRecursive(itemPath, targetPath);
} else {
    // 跳过未包含的 Bundle
    Editor.info(`Skipped excluded bundle directory: ${item}`);
}
```

### 4. Bundle Include 字段管理

#### 4.1 配置文件集成

**YAML 配置中的 include 字段**
```yaml
bundles:
  common:
    version: "1.0"
    include: true    # 布尔值控制是否包含
    dependencies: []
```

#### 4.2 界面状态同步

**双向绑定实现**
- 加载时：从配置文件读取 include 状态到界面
- 更改时：界面变更同步到内存和缓存文件
- 构建时：根据 include 状态决定打包内容

**状态持久化**
- 用户的 Include 选择保存在缓存文件中
- 项目切换时自动恢复对应的 Include 状态
- 构建过程中传递 Include 信息给生成脚本

## 技术实现细节

### 1. 类型安全

**新增类型定义**
```typescript
interface IMainConfig {
    projects: string[];
}

interface IProjectConfig {
    mainPackage: {
        version: string;
        serverAddresses: Record<string, Record<string, string>>;
        exportDirectory: string;
    };
    bundleUpdate: {
        exportDirectory: string;
        serverAddresses: Record<string, Record<string, string>>;
        bundles: Record<string, {
            version: string;
            md5: string;
            url: string;
            include: boolean;  // 新增字段
            dependencies?: string[];
        }>;
    };
}
```

### 2. 向后兼容性

**迁移策略**
- 保留原有的 `IYamlConfig` 接口
- 支持从旧配置文件自动迁移
- 新旧配置系统并存，平滑过渡

**错误处理**
- 配置文件不存在时回退到默认配置
- 项目配置加载失败时提供友好错误信息
- 网络请求失败时的降级处理

### 3. 性能优化

**缓存机制**
- 项目配置按需加载，避免重复读取
- Bundle 状态变更实时同步到缓存
- 最小化文件 I/O 操作

**条件化处理**
- 只复制需要的 Bundle 文件
- 减少不必要的文件操作
- 优化构建时间和输出大小

## 代码质量保证

### 1. ESLint 规范遵守

**严格的代码规范**
- 所有新增代码通过 ESLint 检查
- 统一的代码风格和命名规范
- 适当的错误处理和日志记录

**类型安全**
- 完整的 TypeScript 类型定义
- 严格的类型检查
- 避免 any 类型的使用

### 2. 错误处理

**完善的异常处理**
```typescript
try {
    const allBundleConfigs = ConfigManager.getAllBundleConfigs(selectedProject);
    // 处理逻辑
} catch (error) {
    Editor.error('[bundle-tool] Failed to get bundle configs:', error);
    // 降级处理
}
```

## 使用指南

### 1. 项目配置

1. **创建项目配置文件**：在 `config/` 目录下创建 `{project-name}.yaml`
2. **配置项目信息**：设置主包和 Bundle 更新的相关参数
3. **设置 Bundle Include 状态**：通过 `include` 字段控制是否包含特定 Bundle

### 2. 界面操作

1. **选择项目**：在 Project 下拉框中选择目标项目
2. **配置 Bundle**：勾选或取消勾选 Include 复选框
3. **执行构建**：选择 Main Package 或 Bundle 更新模式进行构建

### 3. 构建流程

**主包更新模式**
1. 选择 "Main Package Update" 模式
2. 配置主包服务器地址和版本
3. 执行 Build/Pack 操作
4. 系统自动根据 Include 状态处理文件

**Bundle 更新模式**
1. 选择 "Bundle Update" 模式
2. 配置 Bundle 参数和 Include 状态
3. 执行构建操作
4. 生成对应的 Bundle 文件

## 总结

本次实现成功完成了 Bundle Tool 的重大升级：

✅ **分层化配置管理**：实现了主配置和项目独立配置的两层结构
✅ **界面交互优化**：添加了 Project 选择器和动态联动功能
✅ **主包构建优化**：支持条件化打包和智能文件复制
✅ **Include 字段管理**：完整的 Bundle 包含状态控制
✅ **代码质量保证**：严格遵守 ESLint 规范，保持高质量代码

所有功能都在现有逻辑基础上进行扩展，保持了良好的向后兼容性，为团队提供了更加灵活和强大的热更新管理工具。
