# Cocos Creator 热更新插件 - 主包更新功能使用指南

## 概述

本插件已扩展支持主包（Main Package）热更新功能，现在可以统一管理 Bundle 更新和主包更新两种模式。

## 新增功能

### 1. 统一配置管理

- **新配置文件**: `config.yaml` 替代原有的 `bundle-server-address.json`
- **配置结构**: 包含 `main_package` 和 `bundle_update` 两个主要节点
- **自动迁移**: 插件会自动从旧配置文件迁移到新格式

### 2. 更新类型切换

在插件界面中，您可以通过 "Update Type" 单选框在两种模式间切换：

- **Bundle Update**: 传统的子模块热更新模式
- **Main Package Update**: 新增的主包热更新模式

### 3. 动态目录管理

切换更新类型时，导出目录会自动更新：

- Bundle 更新: `./build/bundles`
- 主包更新: `./build/main-package`

### 4. 主包配置界面

当选择 "Main Package Update" 时，界面会显示：

- **Base Bundle Server**: 主包服务器地址配置
- **Version**: 主包版本号配置

## 配置文件结构

### config.yaml 示例

```yaml
# 主包更新配置
main_package:
  version: "1.0.0"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/game/h5/"
      IOS: "https://dev.example.com/game/native/"
      ANDROID: "https://dev.example.com/game/native/"
  export_directory: "./build/main-package"

# Bundle更新配置
bundle_update:
  export_directory: "./build/bundles"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/game/h5/"
      IOS: "https://dev.example.com/game/native/"
      ANDROID: "https://dev.example.com/game/native/"
  bundles:
    common:
      version: "1.0"
      url: "http://************:30002/"
```

## 使用流程

### 主包更新流程

1. 在插件界面选择 "Main Package Update"
2. 配置主包服务器地址和版本号
3. 选择目标平台和环境
4. 点击 "Build"、"Pack" 或 "Build and pack" 按钮
5. 插件会生成主包的 `project.manifest` 和 `version.manifest` 文件

### Bundle 更新流程

1. 在插件界面选择 "Bundle Update"
2. 配置各个 Bundle 的版本、URL 和依赖关系
3. 选择要包含的 Bundle
4. 点击操作按钮执行构建和打包

## 技术实现

### 核心文件

- `config-manager.ts`: 统一配置管理器
- `main_package_generator.js`: 主包 manifest 生成脚本
- `panel.ts`: 扩展的 UI 逻辑
- `type.ts`: 新增的类型定义

### 关键特性

- **向后兼容**: 支持从旧配置格式自动迁移
- **类型安全**: 完整的 TypeScript 类型定义
- **错误处理**: 完善的错误处理和日志记录
- **ESLint 兼容**: 符合项目代码规范

## 注意事项

1. 首次使用时，插件会自动从 `bundle-server-address.json` 迁移配置
2. 主包更新模式下，只会处理 `internal`、`main`、`resources` 等核心资源
3. 确保 Cocos Creator 路径在 `.env` 文件中正确配置
4. 不同平台的构建模板选择会影响源文件路径

## 故障排除

如果遇到问题，请检查：

1. `config.yaml` 文件格式是否正确
2. `.env` 文件中的 Cocos Creator 路径是否存在
3. 目标导出目录是否有写入权限
4. 控制台日志中的详细错误信息
