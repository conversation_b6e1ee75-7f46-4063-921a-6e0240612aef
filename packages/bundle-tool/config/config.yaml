# Cocos Creator 热更新插件统一配置文件
# 支持主包更新和Bundle更新两种模式

# 主包更新配置
main_package:
  # 主包版本号
  version: "1.0.0"
  # 主包服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/game/h5/"
      IOS: "https://dev.example.com/game/native/"
      ANDROID: "https://dev.example.com/game/native/"
    STAGE:
      WEB_MOBILE: "https://stage.example.com/game/h5/"
      IOS: "https://stage.example.com/game/native/"
      ANDROID: "https://stage.example.com/game/native/"
    PROD:
      WEB_MOBILE: "https://prod.example.com/game/h5/"
      IOS: "https://prod.example.com/game/native/"
      ANDROID: "https://prod.example.com/game/native/"
  # 主包导出目录配置
  export_directory: "./build/main-package"

# Bundle更新配置
bundle_update:
  # Bundle导出目录配置
  export_directory: "./build/bundles"
  # Bundle服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/game/h5/"
      IOS: "https://dev.example.com/game/native/"
      ANDROID: "https://dev.example.com/game/native/"
    STAGE:
      WEB_MOBILE: "https://stage.example.com/game/h5/"
      IOS: "https://stage.example.com/game/native/"
      ANDROID: "https://stage.example.com/game/native/"
    PROD:
      WEB_MOBILE: "https://prod.example.com/game/h5/"
      IOS: "https://prod.example.com/game/native/"
      ANDROID: "https://prod.example.com/game/native/"
  # Bundle详细配置
  bundles:
    common:
      version: "1.0"
      md5: ""
      url: "http://************:30002/"
    common-portrait:
      version: ""
      md5: ""
      url: "http://************:30003/"
      dependencies:
        - "common"
    common-landscape:
      version: ""
      md5: ""
      url: "http://************:30002/"
      dependencies:
        - "common"
    lobby-common:
      version: ""
      md5: ""
      url: "http://************:30002/"
      dependencies:
        - "common"
    lobby-portrait:
      version: ""
      md5: ""
      url: "http://************:30002/"
      dependencies:
        - "common-portrait"
        - "lobby-common"
    lobby-landscape:
      version: ""
      md5: ""
      url: "http://************:30002"
      dependencies:
        - "common-landscape"
        - "lobby-common"
    texas-common:
      version: ""
      md5: ""
      url: "http://************:30002"
      dependencies:
        - "common"
        - "jackpot"
    texas-portrait:
      version: ""
      md5: ""
      url: "http://************:30002"
      dependencies:
        - "common-portrait"
        - "texas-common"
    texas-landscape:
      version: ""
      md5: ""
      url: "http://************:30002"
      dependencies:
        - "common-landscape"
        - "texas-common"
    mtt-bridge:
      version: ""
      md5: ""
      url: "http://************:30002"
    jackpot:
      version: ""
      md5: ""
      url: "http://************:30002"
      dependencies:
        - "common"
    debug:
      version: ""
      md5: ""
      url: "http://************:30002"
