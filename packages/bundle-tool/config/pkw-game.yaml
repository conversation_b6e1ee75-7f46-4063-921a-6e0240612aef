# PKW Game 项目配置文件
# 包含主包更新和Bundle更新的完整配置

# 主包更新配置
main_package:
  # 主包版本号
  version: "1.0.0"
  # 主包服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.pkw-game.com/h5/"
      IOS: "https://dev.pkw-game.com/native/"
      ANDROID: "https://dev.pkw-game.com/native/"
    STAGE:
      WEB_MOBILE: "https://stage.pkw-game.com/h5/"
      IOS: "https://stage.pkw-game.com/native/"
      ANDROID: "https://stage.pkw-game.com/native/"
    PROD:
      WEB_MOBILE: "https://prod.pkw-game.com/h5/"
      IOS: "https://prod.pkw-game.com/native/"
      ANDROID: "https://prod.pkw-game.com/native/"
  # 主包导出目录配置
  export_directory: "./build/pkw-game/main-package"

# Bundle更新配置
bundle_update:
  # Bundle导出目录配置
  export_directory: "./build/pkw-game/bundles"
  # Bundle服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.pkw-game.com/h5/"
      IOS: "https://dev.pkw-game.com/native/"
      ANDROID: "https://dev.pkw-game.com/native/"
    STAGE:
      WEB_MOBILE: "https://stage.pkw-game.com/h5/"
      IOS: "https://stage.pkw-game.com/native/"
      ANDROID: "https://stage.pkw-game.com/native/"
    PROD:
      WEB_MOBILE: "https://prod.pkw-game.com/h5/"
      IOS: "https://prod.pkw-game.com/native/"
      ANDROID: "https://prod.pkw-game.com/native/"
  # Bundle详细配置
  bundles:
    common-resource:
      version: "1.0"
      md5: ""
      url: "http://************:30002/"
      include: true
      dependencies: []
    common-portrait:
      version: "1.1"
      md5: ""
      url: "http://************:30003/"
      include: true
      dependencies:
        - "common"
    common-landscape:
      version: "1.1"
      md5: ""
      url: "http://************:30002/"
      include: false
      dependencies:
        - "common"
    lobby-common:
      version: "2.0"
      md5: ""
      url: "http://************:30002/"
      include: true
      dependencies:
        - "common"
    lobby-portrait:
      version: "2.1"
      md5: ""
      url: "http://************:30002/"
      include: true
      dependencies:
        - "common-portrait"
        - "lobby-common"
    lobby-landscape:
      version: "2.1"
      md5: ""
      url: "http://************:30002/"
      include: false
      dependencies:
        - "common-landscape"
        - "lobby-common"
    humanboy:
      version: "3.0"
      md5: ""
      url: "http://************:30002/"
      include: true
      dependencies:
        - "lobby-portrait"
    cowboy:
      version: "3.0"
      md5: ""
      url: "http://************:30002/"
      include: true
      dependencies:
        - "lobby-portrait"
    poker-master:
      version: "3.0"
      md5: ""
      url: "http://************:30002/"
      include: false
      dependencies:
        - "lobby-portrait"
