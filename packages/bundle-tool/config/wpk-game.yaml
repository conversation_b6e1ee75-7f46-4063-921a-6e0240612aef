# WPK Game 项目配置文件
# 包含主包更新和Bundle更新的完整配置

# 主包更新配置
main_package:
  # 主包版本号
  version: "2.0.0"
  # 主包服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.wpk-game.com/h5/"
      IOS: "https://dev.wpk-game.com/native/"
      ANDROID: "https://dev.wpk-game.com/native/"
    STAGE:
      WEB_MOBILE: "https://stage.wpk-game.com/h5/"
      IOS: "https://stage.wpk-game.com/native/"
      ANDROID: "https://stage.wpk-game.com/native/"
    PROD:
      WEB_MOBILE: "https://prod.wpk-game.com/h5/"
      IOS: "https://prod.wpk-game.com/native/"
      ANDROID: "https://prod.wpk-game.com/native/"
  # 主包导出目录配置
  export_directory: "./build/wpk-game/main-package"

# Bundle更新配置
bundle_update:
  # Bundle导出目录配置
  export_directory: "./build/wpk-game/bundles"
  # Bundle服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.wpk-game.com/h5/"
      IOS: "https://dev.wpk-game.com/native/"
      ANDROID: "https://dev.wpk-game.com/native/"
    STAGE:
      WEB_MOBILE: "https://stage.wpk-game.com/h5/"
      IOS: "https://stage.wpk-game.com/native/"
      ANDROID: "https://stage.wpk-game.com/native/"
    PROD:
      WEB_MOBILE: "https://prod.wpk-game.com/h5/"
      IOS: "https://prod.wpk-game.com/native/"
      ANDROID: "https://prod.wpk-game.com/native/"
  # Bundle详细配置
  bundles:
    common-resource:
      version: "2.0"
      md5: ""
      url: "http://************:30004/"
      include: true
      dependencies: []
    common-portrait:
      version: "2.1"
      md5: ""
      url: "http://************:30005/"
      include: true
      dependencies:
        - "common"
    common-landscape:
      version: "2.1"
      md5: ""
      url: "http://************:30004/"
      include: true
      dependencies:
        - "common"
    lobby-common:
      version: "3.0"
      md5: ""
      url: "http://************:30004/"
      include: true
      dependencies:
        - "common"
    lobby-portrait:
      version: "3.1"
      md5: ""
      url: "http://************:30004/"
      include: true
      dependencies:
        - "common-portrait"
        - "lobby-common"
    lobby-landscape:
      version: "3.1"
      md5: ""
      url: "http://************:30004/"
      include: true
      dependencies:
        - "common-landscape"
        - "lobby-common"
    game1:
      version: "4.0"
      md5: ""
      url: "http://************:30004/"
      include: true
      dependencies:
        - "lobby-portrait"
    game2:
      version: "4.0"
      md5: ""
      url: "http://************:30004/"
      include: false
      dependencies:
        - "lobby-landscape"
