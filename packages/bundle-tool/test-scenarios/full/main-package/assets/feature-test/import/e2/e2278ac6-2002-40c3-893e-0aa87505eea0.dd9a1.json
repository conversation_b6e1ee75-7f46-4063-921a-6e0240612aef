[1, ["ecpdLyjvZBwrvm+cedCcQy", "f0BIwQ8D5Ml7nTNQbh1YlS", "68J8oyAQdFUrqy37MXmbtE", "90AErWL21A4ZPvtxQ3XG8G", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO"], ["node", "_spriteFrame", "checkMark", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "listNode", "data", "_parent"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color", "_anchorPoint"], 0, 9, 4, 5, 7, 1, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "_N$paddingTop", "_N$spacingY", "_N$paddingLeft", "_N$spacingX", "_resize", "node", "_layoutSize"], -3, 1, 5], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "_N$cacheMode", "_lineHeight", "node", "_materials"], -5, 1, 3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Widget", ["_alignFlags", "alignMode", "_originalWidth", "_originalHeight", "_right", "_top", "node"], -3, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize"], 0, 1, 2, 4, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 1, 1, 5, 1, 1, 9], ["f25cbZ8ES1NhY4HQzWBaTB4", ["node", "listNode"], 3, 1, 1]], [[4, 0, 1, 2], [0, 0, 7, 8, 3, 4, 5, 6, 2], [0, 0, 1, 7, 3, 4, 5, 6, 3], [0, 0, 7, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3, 4, 5, 6, 4], [3, 0, 1, 7, 2, 3, 8, 9, 6], [1, 3, 4, 5, 1], [1, 0, 2, 3, 4, 3], [6, 0, 1, 3, 3], [10, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 3, 4, 6, 7, 4], [7, 0, 2], [0, 0, 8, 3, 4, 5, 6, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 1, 7, 3, 4, 9, 5, 3], [0, 0, 2, 7, 8, 3, 4, 5, 10, 6, 3], [3, 0, 1, 4, 2, 3, 5, 6, 8, 9, 8], [4, 1, 1], [1, 1, 0, 3, 4, 5, 3], [5, 1, 0, 2, 3, 6, 5], [5, 0, 4, 5, 6, 4], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [6, 0, 1, 2, 3, 4], [2, 5, 0, 1, 2, 6, 5], [2, 0, 1, 2, 6, 4], [11, 0, 1, 1]], [[11, "TestMenu"], [12, "TestMenu", [-6, -7], [[20, 33, 10, 10, -2], [24, 2, 20, 20, -3], [25, -5, -4]], [17, -1], [5, 300, 200], [1760, 970, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "Background", 512, [-10], [[18, 1, 0, -8, [1], 2], [19, 0, 45, 100, 40, -9]], [0, "1eK7jca3FJ4rkXy4hnpIEw", 1], [5, 100, 40]], [15, "<PERSON><PERSON>", false, 1, [-12, -13], [[23, 1, 2, 10, 10, -11]], [0, "8avcgxdQpFnb3gO6TihV1o", 1], [5, 300, 200], [0, 0.5, 1], [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "LuckTurntable", 3, [-15, -16], [[10, 1, 20, 20, -14, [5, 300, 50]]], [0, "215GUUtlxFaKas+BPiUAOE", 1], [5, 300, 50], [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Toggle", 4, [-20, -21], [[9, 3, false, -19, [4, **********], -18, -17, [[22, "f25cbZ8ES1NhY4HQzWBaTB4", "toggleEventHandler", "feature-test.test-luck-turntable", 1]]]], [0, "29okMoDcpMPbQKTFhh8oOM", 1], [5, 28, 28], [-116, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "SomethingElse", 3, [-23, -24], [[10, 1, 20, 20, -22, [5, 300, 50]]], [0, "a1p219XxtBBZLcGxFlPEpW", 1], [5, 300, 50], [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Toggle", 6, [-28, -29], [[9, 3, false, -27, [4, **********], -26, -25, [[8, "f25cbZ8ES1NhY4HQzWBaTB4", "toggleEventHandler", 1]]]], [0, "7dI7mM/E5Hpra0tzdj3CQ0", 1], [5, 28, 28], [-116, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "MenuButton", 1, [2], [[21, 2, -30, [[8, "f25cbZ8ES1NhY4HQzWBaTB4", "toggleList", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 2, 3, 4, 5, 6]], [0, "b9CWRIMppAPKv043GhzMfQ", 1], [5, 100, 40], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Background", 512, 5, [[6, -31, [7], 8]], [0, "06COQLROZP/pgsK+akQOpP", 1], [5, 28, 28], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "Background", 512, 7, [[6, -32, [11], 12]], [0, "c0pqabjNVKfKpv1uU7jQFx", 1], [5, 28, 28], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "Label", 512, 2, [[16, "feature", 20, false, 1, 1, 1, 1, -33, [0]]], [0, "daC1Vix4dMm6j6c1MJEWYq", 1], [4, 4278190080], [5, 100, 40]], [4, "checkmark", 512, false, 5, [-34], [0, "45M4ZPUU5DUZcGRu0Bhrt/", 1], [5, 28, 28]], [7, 2, false, 12, [9]], [3, "Label", 4, [[5, "Luck Turntable", 24, 24, 1, 1, -35, [10]]], [0, "41TuXK+v5FjJ+X0XeVeDUm", 1], [5, 157.86, 30.24], [-3.069999999999993, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "checkmark", 512, false, 7, [-36], [0, "cdY+iT7HFA/qD/DVt4Wgcl", 1], [5, 28, 28]], [7, 2, false, 15, [13]], [3, "Label", 6, [[5, "Something Else", 24, 24, 1, 1, -37, [14]]], [0, "b0z1W84m1HP7eF7QNRA4Bq", 1], [5, 168.09, 30.24], [2.0450000000000017, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 8, 1, 0, 0, 1, 0, 0, 1, 0, 9, 3, 0, 0, 1, 0, -1, 8, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -1, 11, 0, 0, 3, 0, -1, 4, 0, -2, 6, 0, 0, 4, 0, -1, 5, 0, -2, 14, 0, 2, 13, 0, 3, 9, 0, 0, 5, 0, -1, 9, 0, -2, 12, 0, 0, 6, 0, -1, 7, 0, -2, 17, 0, 2, 16, 0, 3, 10, 0, 0, 7, 0, -1, 10, 0, -2, 15, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, 0, 14, 0, -1, 16, 0, 0, 17, 0, 10, 1, 2, 11, 8, 37], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 16], [-1, -1, 1, 4, 5, 6, 7, -1, 1, -1, -1, -1, 1, -1, -1, 1, 1], [0, 0, 1, 1, 4, 1, 5, 0, 2, 0, 0, 0, 2, 0, 0, 3, 3]]