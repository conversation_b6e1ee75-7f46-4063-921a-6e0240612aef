{"version": 3, "sources": ["assets/bundles/game1/scripts/components/Game1Logic.ts", "assets/bundles/game1/scripts/game1-entry.ts"], "names": ["pf", "require", "_a", "cc", "_decorator", "ccclass", "property", "Game1Logic", "_super", "__extends", "_this", "apply", "this", "arguments", "background", "actor", "prototype", "start", "update", "dt", "x", "onClickSceneBack", "bundleManager", "exitBundle", "__decorate", "Node", "Component", "exports", "default", "pf_1", "Game1Entry", "call", "bundleType", "BUNDLE_TYPE", "BUNDLE_GAME", "onLoad", "__awaiter", "Promise", "log", "bundle", "name", "resolve", "onEnter", "reject", "loadScene", "err", "scene", "console", "warn", "director", "runScene", "onExit", "onUnload", "registerEntry", "BundleEntry"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,IAAAC,EAAA,2CAEMC,IAAAC,GAAAC,YAAAC,IAAAH,EAAAG,SAAAC,IAAAJ,EAAAI,UAGNC,IAAA,SAAAC;AAAwCC,EAAAF,GAAAC;AAAxC,SAAAD;AAAA,IAAAG,IAAA,SAAAF,KAAAA,EAAAG,MAAAC,MAAAC,cAAAD;AAEIF,EAAAI,aAAA;AAEAJ,EAAAK,QAAA;;;AAMAR,EAAAS,UAAAC,QAAA;AAEAV,EAAAS,UAAAE,SAAA,SAAAC;AACIP,KAAAE,WAAAM,KAAA,MAAAD;AACAP,KAAAE,WAAAM,MAAA,SACIR,KAAAE,WAAAM,KAAA;;AAGRb,EAAAS,UAAAK,mBAAA;AACIrB,EAAAsB,cAAAC,WAAA;;AAjBJ<PERSON>,EAAA,EADClB,EAAAH,GAAAsB,SAC0BlB,EAAAS,WAAA,mBAAA;AAE3BQ,EAAA,EADClB,EAAAH,GAAAsB,SACqBlB,EAAAS,WAAA,cAAA;AAiB1B,OArBqBQ,EAAA,EADpBnB,KACoBE;CAArB,CAAAJ,GAAAuB;AAAqBC,EAAAC,UAAArB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXrB,IAAAsB,IAAA5B,EAAA,wCAGA6B,IAAA,SAAAtB;AAAgCC,EAAAqB,GAAAtB;AAC5B,SAAAsB;AAAA,IAAApB,IAAAF,EAAAuB,KAAAnB,SAAAA;AAEIF,EAAAsB,aAAAH,EAAAI,YAAAC;;;AAGEJ,EAAAd,UAAAmB,SAAA;AAAkC,OAAAC,EAAAxB,WAAA,GAAAyB,SAAA;;AACpClC,GAAAmC,IAAA1B,KAAA2B,OAAAC,OAAA;AAEA,OAAA,EAAA,GAAAH,QAAAI;;;;AAGEX,EAAAd,UAAA0B,UAAA;AAAmC,OAAAN,EAAAxB,WAAA,GAAAyB,SAAA;;;AACrClC,GAAAmC,IAAA1B,KAAA2B,OAAAC,OAAA;AAEA,OAAA,EAAA,GAAA,IAAAH,QAAA,SAAAI,GAAAE;AACIjC,EAAA6B,OAAAK,UAAA,SAAA,SAAAC,GAAAC;AACI,IAAAD,GAAA;AACIE,QAAAC,KAAAH;AACAF,EAAAE;OAEA1C,GAAA8C,SAAAC,SAAAJ,GAAA;AACIL;;;;;;;AAOVX,EAAAd,UAAAmC,SAAA;AAAV,IAAAzC,IAAAE;AACI,OAAA,IAAAyB,QAAA,SAAAI;AACItC,GAAAmC,IAAA5B,EAAA6B,OAAAC,OAAA;AACAC;;;AAIRX,EAAAd,UAAAoC,WAAA;AACIjD,GAAAmC,IAAA1B,KAAA2B,OAAAC,OAAA;;AAER,OAvCahB,EAAA,EADZK,EAAAwB,cAAA,YACYvB;CAAb,CAAAD,EAAAyB;AAAa3B,EAAAG,aAAAA", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html\n\nimport * as pf from '../../../../poker-framework/scripts/pf';\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class Game1Logic extends cc.Component {\n    @property(cc.Node)\n    background: cc.Node = null;\n    @property(cc.Node)\n    actor: cc.Node = null;\n\n    // LIFE-CYCLE CALLBACKS:\n\n    // onLoad () {}\n\n    start() {}\n\n    update(dt) {\n        this.background.x -= dt * 100;\n        if (this.background.x <= -(1024 + 256)) {\n            this.background.x = -256;\n        }\n    }\n    onClickSceneBack() {\n        pf.bundleManager.exitBundle('game1');\n    }\n}\n", "import type { IBundleOptions } from '../../../poker-framework/scripts/pf';\nimport { BUNDLE_TYPE, BundleEntry, registerEntry } from '../../../poker-framework/scripts/pf';\n\n@registerEntry('game1')\nexport class Game1Entry extends BundleEntry {\n    constructor() {\n        super();\n        this.bundleType = BUNDLE_TYPE.BUNDLE_GAME;\n    }\n\n    async onLoad(options?: IBundleOptions): Promise<void> {\n        cc.log(`${this.bundle.name} onLoad`);\n\n        return Promise.resolve();\n    }\n\n    async onEnter(options?: IBundleOptions): Promise<void> {\n        cc.log(`${this.bundle.name} onEnter`);\n\n        return new Promise<void>((resolve, reject) => {\n            this.bundle.loadScene('game1', (err, scene) => {\n                if (err) {\n                    console.warn(err);\n                    reject(err);\n                } else {\n                    cc.director.runScene(scene, () => {\n                        resolve();\n                    });\n                }\n            });\n        });\n    }\n\n    protected onExit(): Promise<void> {\n        return new Promise((resolve) => {\n            cc.log(`${this.bundle.name} onExit`);\n            resolve();\n        });\n    }\n\n    onUnload(): void {\n        cc.log(`${this.bundle.name} onUnload`);\n    }\n}\n"], "file": "index.js"}