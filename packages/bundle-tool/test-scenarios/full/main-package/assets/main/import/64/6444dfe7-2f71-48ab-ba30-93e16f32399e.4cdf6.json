[1, ["ecpdLyjvZBwrvm+cedCcQy", "f0BIwQ8D5Ml7nTNQbh1YlS", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO", "90AErWL21A4ZPvtxQ3XG8G", "a2MjXRFdtLlYQ5ouAFv/+R"], ["node", "_spriteFrame", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "_N$normalSprite", "safearea", "layout", "in_button", "yn_button", "en_button", "ch_button", "title_text", "btn_back", "data", "_parent"], [["cc.Widget", ["_alignFlags", "_originalWidth", "_isAbsLeft", "_isAbsRight", "_originalHeight", "_left", "_right", "_bottom", "_top", "alignMode", "_enabled", "node"], -8, 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_parent", "_anchorPoint", "_children", "_trs", "_color"], 1, 9, 4, 5, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_children", "_color"], 2, 1, 12, 4, 5, 5, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 2, 4, 5, 7], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["244f9nlJENGzI4gsBJoi3Ke", ["node", "btn_back", "title_text", "ch_button", "en_button", "yn_button", "in_button", "layout", "safearea"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[4, 0, 1, 2], [1, 0, 5, 2, 3, 4, 8, 2], [2, 1, 0, 2, 3, 3], [0, 0, 1, 11, 3], [5, 0, 1, 4, 2, 3, 8, 9, 6], [3, 0, 1, 7, 2, 3, 8, 4, 5, 6, 2], [2, 2, 3, 4, 1], [0, 0, 5, 2, 11, 4], [0, 0, 6, 3, 11, 4], [6, 1, 2, 3, 4, 1], [1, 0, 5, 2, 3, 4, 6, 8, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 5, 6, 8, 2, 3, 1, 4, 11, 9], [0, 0, 5, 6, 2, 3, 1, 11, 7], [7, 0, 2], [1, 0, 7, 2, 3, 4, 8, 2], [1, 0, 5, 2, 3, 9, 4, 2], [1, 0, 5, 2, 3, 4, 6, 2], [1, 0, 5, 7, 2, 3, 4, 6, 2], [1, 0, 5, 2, 3, 9, 4, 6, 2], [1, 0, 1, 5, 7, 2, 3, 4, 3], [1, 0, 1, 5, 2, 3, 9, 4, 3], [3, 0, 1, 7, 2, 3, 4, 5, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3, 4, 5, 2], [2, 0, 2, 3, 4, 2], [2, 0, 2, 3, 2], [0, 0, 2, 3, 1, 4, 11, 6], [0, 0, 11, 2], [0, 9, 0, 1, 4, 11, 5], [0, 0, 5, 6, 7, 2, 3, 1, 4, 11, 9], [0, 10, 0, 5, 6, 8, 7, 1, 4, 11, 9], [4, 1, 1], [5, 0, 1, 5, 2, 3, 6, 7, 8, 9, 8], [6, 0, 1, 5, 2, 3, 4, 2], [9, 0, 1, 2, 3, 3], [10, 0, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1]], [[14, "LanguageSetting"], [15, "LanguageSetting", [-14, -15], [[26, 0, -2, [29]], [31, false, 45, 530, 530, 100, 100, 1080, 2338, -3], [36, -4], [37, -13, -12, -11, -10, -9, -8, -7, -6, -5]], [32, -1], [5, 400, 600], [968.684, 731.153, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "LayoutParent", 1, [-18, -19, -20, -21, -22, -23], [[-16, [3, 41, 1080, -17]], 1, 4], [0, "64OitVyD9IurWc03L3eQN3", 1], [5, 400, 500], [0, 0.5, 1], [0, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ch_button", 2, [-27, -28, -29, -30], [[[2, 1, 0, -24, [13]], -25, [3, 40, 1080, -26]], 4, 1, 4], [0, "9aA6R8YrpAqaGeLU7Ku6+t", 1], [4, 4281476903], [5, 400, 100], [0, 0.5, 1], [0, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "en_button", 2, [-34, -35, -36], [[[2, 1, 0, -31, [18]], -32, [3, 40, 1080, -33]], 4, 1, 4], [0, "b8wdflbxNP1o0hMOObGdLh", 1], [4, 4281476903], [5, 400, 100], [0, 0.5, 1], [0, -200, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "yn_button", 2, [-40, -41, -42], [[[2, 1, 0, -37, [23]], -38, [3, 40, 1080, -39]], 4, 1, 4], [0, "bcrsXi3rdPFIXH239B7YBc", 1], [4, 4281476903], [5, 400, 100], [0, 0.5, 1], [0, -300, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "in_button", 2, [-46, -47, -48], [[[2, 1, 0, -43, [28]], -44, [3, 40, 1080, -45]], 4, 1, 4], [0, "8dAsPFynZOqpbfoTTozODz", 1], [4, 4281476903], [5, 400, 100], [0, 0.5, 1], [0, -400, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "BackButton", [-51], [-50], [0, "68b5/+OnZOJocYHXs1AQ04", -49], [5, 50, 40], [-152.935, -51.163, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "Title", 2, [-53, -54, 7], [[3, 40, 1080, -52]], [0, "bcmjuG3ARIIZI0+LMQ1MYK", 1], [5, 400, 100], [0, 0.5, 1]], [20, "Background", 512, 7, [-57], [[11, 1, 0, -55, [6], 7], [29, 0, 45, 100, 40, -56]], [0, "abgf9KGgdMsahHlBHOj/Jv", 7], [5, 50, 40]], [16, "img_bg", 1, [[11, 1, 0, -58, [0], 1], [27, 45, false, false, 1080, 2338, -59]], [0, "15Z54I4VZKeq0s8vKulm+R", 1], [4, 4281476903], [5, 400, 600]], [17, "SafeArea", 2, [[3, 40, 1080, -60]], [0, "bdt4kCKEpOI4xuDxP5xBYC", 1], [5, 400, 0], [0, 0.5, 1]], [19, "img_top_bg", 8, [[25, 0, -61, [2], 3], [3, 40, 860, -62]], [0, "fboFZSS1hBJKFTMJpaHnCQ", 1], [4, 4281476903], [5, 400, 150], [0, 0.5, 1]], [23, "txtTitle", 8, [[-63, [28, 2, -64]], 1, 4], [0, "8dqeaOgv9CB6t+tq2Cnifj", 1], [5, 138, 80.64], [0, 0.5, 1], [0, -9.68, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 3, [[2, 1, 0, -65, [8]], [30, 45, 0.013888888888888895, 0.013888888888888895, 148, false, false, 1080, 2, -66]], [0, "b21crnzadG3JKyRQe+bvNO", 1], [5, 388.88888888888886, -48], [0, 24, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "text", 3, [[4, "语言设置", 37, 60, 1, 1, -67, [9]], [7, 10, 0.07037037037037037, false, -68]], [0, "c0Jzd1+j9Fv7mc8LGTJIo4", 1], [5, 148, 75.6], [0, 0.5, 1], [-97.85185185185185, -12.200000000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "hook", 3, [[6, -69, [10], 11], [8, 34, 0.1139351851851852, false, -70]], [0, "46/hJdG5tDEoNIAwzI83v/", 1], [5, 20, 18], [0, 0.5, 1], [144.42592592592592, -41, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "line", 3, [[2, 1, 0, -71, [12]], [12, 45, 0.01388888888888894, 0.01388888888888894, 148, false, false, 1080, 2, -72]], [0, "68DqBUZfRDJJSXneMr0WXL", 1], [5, 388.88888888888886, -48], [0, 0.5, 1], [0, -148, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "text", 4, [[4, "语言设置", 37, 60, 1, 1, -73, [14]], [7, 10, 0.07037037037037037, false, -74]], [0, "26tgN8RFdBbYrTD3OuGlaF", 1], [5, 148, 75.6], [-97.85185185185185, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hook", 4, [[6, -75, [15], 16], [8, 34, 0.1139351851851852, false, -76]], [0, "7fsDIl1gtOC56ma7dlvqcI", 1], [5, 20, 18], [144.42592592592592, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 4, [[2, 1, 0, -77, [17]], [12, 45, 0.01388888888888884, 0.01388888888888884, 148, false, false, 1080, 2, -78]], [0, "f4xoR0wuBEErA/Hl/c2j6u", 1], [5, 388.8888888888889, -48], [0, -124, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "text", 5, [[4, "语言设置", 37, 60, 1, 1, -79, [19]], [7, 10, 0.07037037037037037, false, -80]], [0, "f5pnzSXt5LcKpjxc3jBBYF", 1], [5, 148, 75.6], [-97.85185185185185, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hook", 5, [[6, -81, [20], 21], [8, 34, 0.11393518518518522, false, -82]], [0, "77aqI/u5dCIaF6w/8J3ftj", 1], [5, 20, 18], [144.42592592592592, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 5, [[2, 1, 0, -83, [22]], [13, 40, 0.013888888888888895, 0.013888888888888895, false, false, 1080, -84]], [0, "bdIR5LQGhEsoysioSHhWsf", 1], [5, 388.88888888888886, 2], [0, -149, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "text", 6, [[4, "语言设置", 37, 60, 1, 1, -85, [24]], [7, 10, 0.07037037037037037, false, -86]], [0, "245+1PxNBOPq6q6i7JxIBy", 1], [5, 148, 75.6], [-97.85185185185185, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hook", 6, [[6, -87, [25], 26], [8, 34, 0.1139351851851852, false, -88]], [0, "e1c3EjLfFJqZ0stWjK7GGS", 1], [5, 20, 18], [144.42592592592592, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 6, [[2, 1, 0, -89, [27]], [13, 40, 0.013888888888888895, 0.013888888888888895, false, false, 1080, -90]], [0, "b01pJcTH9IupLzMBpOt59K", 1], [5, 388.88888888888886, 2], [0, -149, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "多语言", 46, 64.8, 1, 1, 13, [4]], [21, "Label", 512, 9, [[33, "<", 20, false, 1, 1, 1, 1, -91, [5]]], [0, "89ZOGI9GxIA7ukuWhMzutw", 7], [4, 4278190080], [5, 100, 40]], [34, 2, 7, [4, 4293322470], [4, 4291348680], [4, 3363338360], 9], [9, 3, [4, 4294967295], [4, 4294967295], 3], [9, 4, [4, 4294967295], [4, 4294967295], 4], [9, 5, [4, 4294967295], [4, 4294967295], 5], [9, 6, [4, 4294967295], [4, 4294967295], 6], [35, 1, 2, 2, [5, 400, 500]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 7, 11, 0, 8, 34, 0, 9, 33, 0, 10, 32, 0, 11, 31, 0, 12, 30, 0, 13, 27, 0, 14, 29, 0, 0, 1, 0, -1, 10, 0, -2, 2, 0, -1, 34, 0, 0, 2, 0, -1, 11, 0, -2, 8, 0, -3, 3, 0, -4, 4, 0, -5, 5, 0, -6, 6, 0, 0, 3, 0, -2, 30, 0, 0, 3, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, 0, 4, 0, -2, 31, 0, 0, 4, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, 0, 5, 0, -2, 32, 0, 0, 5, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, 0, 6, 0, -2, 33, 0, 0, 6, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, 5, 7, 0, -1, 29, 0, -1, 9, 0, 0, 8, 0, -1, 12, 0, -2, 13, 0, 0, 9, 0, 0, 9, 0, -1, 28, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 27, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 28, 0, 15, 1, 7, 16, 8, 91], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 29, 29, 29, 30, 30, 30, 31, 31, 31, 32, 32, 32, 33, 33, 33], [-1, 1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, 6, 2, 3, 4, 2, 3, 4, 2, 3, 4, 2, 3, 4, 2, 3, 4], [0, 5, 0, 5, 0, 0, 0, 1, 0, 0, 0, 4, 0, 0, 0, 0, 4, 0, 0, 0, 0, 4, 0, 0, 0, 0, 4, 0, 0, 0, 1, 2, 1, 3, 2, 1, 3, 2, 1, 3, 2, 1, 3, 2, 1, 3]]