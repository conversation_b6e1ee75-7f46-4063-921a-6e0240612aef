# Bundle Tool "Project config not loaded" 错误修复总结

## 问题描述

在构建过程中出现错误：
```
[bundle-tool] Failed to sync bundle list from config: Error: Project config not loaded
```

## 问题原因分析

1. **初始化时机问题**：在某些情况下，`syncBundleListFromConfig` 函数被调用时，项目配置还没有正确加载。

2. **错误处理不当**：当 `projectName` 参数为 `undefined` 或 `null` 时，`ConfigManager` 的相关方法会抛出 "Project config not loaded" 错误，而不是优雅地处理这种情况。

3. **缺少降级机制**：没有为无项目配置的情况提供合适的降级处理。

## 修复方案

### 1. 优化 ConfigManager 错误处理

**修改前**：
```typescript
static getAllBundleConfigs(projectName?: string): Record<string, any> {
    if (projectName) {
        this.loadProjectConfig(projectName);
    }

    if (!this.projectConfig) {
        throw new Error('Project config not loaded');  // 直接抛出错误
    }

    return this.projectConfig.bundleUpdate.bundles;
}
```

**修改后**：
```typescript
static getAllBundleConfigs(projectName?: string): Record<string, any> {
    if (projectName) {
        this.loadProjectConfig(projectName);
    }

    // 如果有项目配置，返回项目配置中的 bundles
    if (this.projectConfig) {
        return this.projectConfig.bundleUpdate.bundles;
    }

    // 如果没有项目配置，尝试使用旧配置
    if (this.legacyConfig && this.legacyConfig.bundleUpdate && this.legacyConfig.bundleUpdate.bundles) {
        return this.legacyConfig.bundleUpdate.bundles;
    }

    // 如果都没有，返回空对象而不是抛出错误
    Editor.warn('[bundle-tool] No project config loaded, returning empty bundle configs');
    return {};
}
```

### 2. 修复 getMainPackageVersion 方法

**修改前**：
```typescript
static getMainPackageVersion(projectName?: string): string {
    if (projectName) {
        this.loadProjectConfig(projectName);
    }

    if (!this.projectConfig) {
        throw new Error('Project config not loaded');  // 直接抛出错误
    }

    return this.projectConfig.mainPackage.version;
}
```

**修改后**：
```typescript
static getMainPackageVersion(projectName?: string): string {
    if (projectName) {
        this.loadProjectConfig(projectName);
    }

    // 如果有项目配置，返回项目配置中的版本
    if (this.projectConfig) {
        return this.projectConfig.mainPackage.version;
    }

    // 如果没有项目配置，尝试使用旧配置
    if (this.legacyConfig && this.legacyConfig.mainPackage && this.legacyConfig.mainPackage.version) {
        return this.legacyConfig.mainPackage.version;
    }

    // 如果都没有，返回默认版本
    Editor.warn('[bundle-tool] No project config loaded, returning default version');
    return '1.0.0';
}
```

### 3. 修复 getBundleConfig 方法

**修改后**：
```typescript
static getBundleConfig(bundleName: string, projectName?: string): any {
    if (projectName) {
        this.loadProjectConfig(projectName);
    }

    // 如果有项目配置，返回项目配置中的 bundle
    if (this.projectConfig) {
        return this.projectConfig.bundleUpdate.bundles[bundleName] || null;
    }

    // 如果没有项目配置，尝试使用旧配置
    if (this.legacyConfig && this.legacyConfig.bundleUpdate && this.legacyConfig.bundleUpdate.bundles) {
        return this.legacyConfig.bundleUpdate.bundles[bundleName] || null;
    }

    // 如果都没有，返回 null
    return null;
}
```

### 4. 增强初始化逻辑

在 `created()` 方法中添加项目配置初始化：

```typescript
// 确保项目配置正确初始化
const extendedView = view as IVueDataExtended;
if (extendedView.selectedProject) {
    try {
        ConfigManager.setCurrentProject(extendedView.selectedProject);
        Editor.info(`[bundle-tool] Initialized with project: ${extendedView.selectedProject}`);
    } catch (error) {
        Editor.warn('[bundle-tool] Failed to initialize project config:', error);
    }
}
```

## 修复效果

### 1. 错误处理优化

- **之前**：遇到配置加载问题时直接抛出错误，导致整个构建流程中断
- **现在**：优雅地处理配置缺失情况，提供降级机制，确保构建流程能够继续

### 2. 向后兼容性

- 支持新的分层配置系统
- 兼容旧的配置文件格式
- 在没有配置文件时提供默认值

### 3. 用户体验改善

- 减少了构建过程中的错误中断
- 提供了更友好的警告信息
- 支持渐进式配置迁移

## 使用建议

### 1. 配置文件检查

确保以下配置文件存在并格式正确：

```
packages/bundle-tool/config/
├── config.yaml          # 主配置文件
├── pkw-game.yaml        # 项目配置文件
└── wpk-game.yaml        # 项目配置文件
```

### 2. 主配置文件示例

`config.yaml`:
```yaml
# 项目列表配置
projects:
  - pkw-game
  - wpk-game
```

### 3. 项目配置文件示例

`pkw-game.yaml`:
```yaml
# 主包更新配置
main_package:
  version: "1.0.0"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/h5/"
  export_directory: "./build/pkw-game/main-package"

# Bundle更新配置
bundle_update:
  export_directory: "./build/pkw-game/bundles"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/h5/"
  bundles:
    common:
      version: "1.0"
      include: true
      dependencies: []
```

## 测试验证

1. **无配置文件测试**：删除配置文件，验证系统能够正常启动并提供默认值
2. **部分配置测试**：只提供主配置文件，验证项目配置缺失时的处理
3. **完整配置测试**：提供完整配置，验证所有功能正常工作

## 总结

通过这次修复，Bundle Tool 现在具备了更强的容错能力和更好的用户体验。系统能够在各种配置情况下稳定运行，为用户提供了更可靠的热更新管理工具。
