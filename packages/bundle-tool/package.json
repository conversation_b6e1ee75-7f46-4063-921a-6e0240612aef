{"name": "bundle-tool", "version": "0.0.1", "description": "The package template for getting started.", "author": "Cocos Creator", "main": "./dist/extension.js", "main-menu": {"BundleTool/Pack Bundle": {"message": "bundle-tool:openPackPanel"}, "BundleTool/Replace uuid": {"message": "bundle-tool:replaceUuid"}}, "panel": {"main": "dist/pack-bundle/panel.js", "type": "dockable", "title": "bundle-tool", "width": 800, "height": 600}, "scripts": {"build": "tsc -b", "watch": "tsc -w", "postinstall": "npm run build"}, "dependencies": {"chalk": "^4.1.2", "fs-extra": "^11.2.0", "js-yaml": "^4.1.0", "minimist": "^1.2.8", "mocha": "^10.4.0", "uuid": "^9.0.1", "yargs": "^17.7.2"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/js-yaml": "^4.0.9", "@types/minimist": "^1.2.5", "@types/node": "^20.14.10", "@types/uuid": "^10.0.0", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-alloy": "^5.1.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-autofix": "^1.1.0", "eslint-plugin-eqeqeq-fix": "^1.0.3", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "3.0.3", "typescript": "^5.2.2"}}