#!/usr/bin/env node

/**
 * Bundle Tool 自动化构建脚本
 * 
 * 用法:
 * node scripts/build.js --config ./config/pkw-game.yaml --target all --output ./dist
 * 
 * 参数:
 * --config: 配置文件路径 (必需)
 * --target: 构建目标 (main|bundle|all) (可选，默认: all)
 * --output: 输出目录 (可选，使用配置文件中的路径)
 * --verbose: 详细日志 (可选)
 * --help: 显示帮助信息
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { exec } = require('child_process');

// 全局配置
let config = null;
let verbose = false;
let projectPath = null;

// 日志工具
const Logger = {
    info: (message) => console.log(`[INFO] ${message}`),
    warn: (message) => console.warn(`[WARN] ${message}`),
    error: (message) => console.error(`[ERROR] ${message}`),
    verbose: (message) => verbose && console.log(`[VERBOSE] ${message}`),
    success: (message) => console.log(`[SUCCESS] ${message}`)
};

// 错误处理
function exitWithError(message, code = 1) {
    Logger.error(message);
    process.exit(code);
}

// 解析命令行参数
function parseArguments() {
    const args = process.argv.slice(2);
    const options = {
        config: null,
        project: null,
        platform: 'web-mobile',
        environment: 'DEV',
        target: 'all',
        output: null,
        verbose: false,
        help: false,
        skipBuild: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--config':
                options.config = args[++i];
                break;
            case '--project':
            case '-p':
                options.project = args[++i];
                break;
            case '--platform':
                options.platform = args[++i];
                break;
            case '--environment':
            case '--env':
            case '-e':
                options.environment = args[++i];
                break;
            case '--target':
                options.target = args[++i];
                break;
            case '--output':
                options.output = args[++i];
                break;
            case '--skip-build':
                options.skipBuild = true;
                break;
            case '--verbose':
                options.verbose = true;
                break;
            case '--help':
                options.help = true;
                break;
            default:
                exitWithError(`Unknown argument: ${args[i]}`);
        }
    }

    return options;
}

// 显示帮助信息
function showHelp() {
    console.log(`
Bundle Tool 自动化构建脚本

用法:
  node scripts/build.js [options]

参数:
  --config <path>        配置文件路径 (可选，可通过 --project 自动推导)
  --project <name>       项目名称 (如: pkw-game, wpk-game)
  --platform <platform>  目标平台: web-mobile|android|ios (默认: web-mobile)
  --environment <env>    环境: DEV|STAGE|PROD (默认: DEV)
  --target <type>        构建目标: main|bundle|all (默认: all)
  --output <path>        输出目录 (可选，使用配置文件中的路径)
  --skip-build          跳过 Cocos Creator 构建，直接打包
  --verbose             显示详细日志
  --help                显示此帮助信息

示例:
  node scripts/build.js --project pkw-game --platform android --environment PROD
  node scripts/build.js --config ./config/pkw-game.yaml --platform ios --verbose
  node scripts/build.js --project wpk-game --target main --environment STAGE
  node scripts/build.js --project pkw-game --skip-build --target bundle
`);
}

// 加载配置文件
function loadConfig(configPath) {
    Logger.info(`Loading configuration from: ${configPath}`);
    
    if (!fs.existsSync(configPath)) {
        exitWithError(`Configuration file not found: ${configPath}`);
    }

    try {
        const yamlContent = fs.readFileSync(configPath, 'utf-8');
        const parsedConfig = yaml.load(yamlContent);
        
        Logger.verbose(`Configuration loaded: ${JSON.stringify(parsedConfig, null, 2)}`);
        
        // 验证配置结构
        if (!parsedConfig.main_package && !parsedConfig.bundle_update) {
            exitWithError('Invalid configuration: missing main_package or bundle_update sections');
        }
        
        return parsedConfig;
    } catch (error) {
        exitWithError(`Failed to load configuration: ${error.message}`);
    }
}

// 验证环境
function validateEnvironment() {
    Logger.info('Validating environment...');
    
    // 检查项目路径
    projectPath = process.cwd();
    const packageJsonPath = path.join(projectPath, 'package.json');
    
    if (!fs.existsSync(packageJsonPath)) {
        exitWithError('Not in a valid project directory (package.json not found)');
    }
    
    // 检查 Cocos Creator 环境变量
    const envPath = path.join(projectPath, 'temp', 'env_variables.json');
    if (!fs.existsSync(envPath)) {
        exitWithError('Cocos Creator environment not configured (temp/env_variables.json not found)');
    }
    
    Logger.success('Environment validation passed');
}

// 执行命令的 Promise 包装
function execCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
        Logger.verbose(`Executing: ${command}`);
        
        exec(command, { cwd: projectPath, ...options }, (error, stdout, stderr) => {
            if (error) {
                Logger.error(`Command failed: ${command}`);
                Logger.error(`Error: ${error.message}`);
                if (stderr) Logger.error(`Stderr: ${stderr}`);
                reject(error);
            } else {
                Logger.verbose(`Command output: ${stdout}`);
                if (stderr) Logger.verbose(`Stderr: ${stderr}`);
                resolve({ stdout, stderr });
            }
        });
    });
}

// 平台映射
const PLATFORM_MAP = {
    'web-mobile': 'web-mobile',
    'web': 'web-mobile',
    'h5': 'web-mobile',
    'android': 'android',
    'ios': 'ios',
    'iphone': 'ios'
};

// 生成构建命令
function generateBuildCommand(platform = 'web-mobile', environment = 'DEV') {
    const envPath = path.join(projectPath, 'temp', 'env_variables.json');
    const envConfig = JSON.parse(fs.readFileSync(envPath, 'utf-8'));

    if (!envConfig.cocosCreatorPath || !fs.existsSync(envConfig.cocosCreatorPath)) {
        exitWithError(`Cocos Creator not found: ${envConfig.cocosCreatorPath}`);
    }

    // 标准化平台名称
    const normalizedPlatform = PLATFORM_MAP[platform.toLowerCase()] || platform;

    // 根据平台和环境生成构建选项
    const buildOptions = {
        platform: normalizedPlatform,
        debug: environment === 'DEV',
        md5Cache: true,
        sourceMaps: environment === 'DEV',
        optimize: environment === 'PROD'
    };

    // 针对不同平台的特殊配置
    switch (normalizedPlatform) {
        case 'android':
            buildOptions.nativeBuildTemplate = 'link';
            buildOptions.apiLevel = 'android-28';
            break;
        case 'ios':
            buildOptions.nativeBuildTemplate = 'link';
            buildOptions.iosDeploymentTarget = '11.0';
            break;
        case 'web-mobile':
            buildOptions.inlineSpriteFrames = true;
            buildOptions.mergeStartScene = environment === 'PROD';
            break;
    }

    const buildString = JSON.stringify(buildOptions);
    Logger.verbose(`Build options: ${buildString}`);

    return `"${envConfig.cocosCreatorPath}" --path "${projectPath}" --build "${buildString}"`;
}

// Cocos Creator 构建
async function buildCocosProject(platform, environment) {
    Logger.info(`Building Cocos Creator project for ${platform} (${environment})...`);

    try {
        const command = generateBuildCommand(platform, environment);
        await execCommand(command);
        Logger.success(`Cocos Creator build completed for ${platform}`);
    } catch (error) {
        exitWithError(`Cocos Creator build failed: ${error.message}`);
    }
}

// 主包构建
async function buildMainPackage(platform, environment, skipBuild = false) {
    Logger.info('Building main package...');

    try {
        if (!skipBuild) {
            await buildCocosProject(platform, environment);
        }
        Logger.success('Main package build completed');
    } catch (error) {
        exitWithError(`Main package build failed: ${error.message}`);
    }
}

// Bundle 构建
async function buildBundles(platform, environment, skipBuild = false) {
    Logger.info('Building bundles...');

    try {
        if (!skipBuild) {
            await buildCocosProject(platform, environment);
        }
        Logger.success('Bundle build completed');
    } catch (error) {
        exitWithError(`Bundle build failed: ${error.message}`);
    }
}

// 获取平台构建路径
function getPlatformBuildPath(platform) {
    const normalizedPlatform = PLATFORM_MAP[platform.toLowerCase()] || platform;
    return path.join(projectPath, 'build', normalizedPlatform);
}

// 主包打包
async function packMainPackage(platform, environment, outputDir) {
    Logger.info(`Packing main package for ${platform} (${environment})...`);

    try {
        const sourcePath = getPlatformBuildPath(platform);
        const targetPath = outputDir || config.main_package.export_directory || path.join(projectPath, 'build', 'main-package', platform);

        // 确保目标目录存在
        if (!fs.existsSync(targetPath)) {
            fs.mkdirSync(targetPath, { recursive: true });
        }

        // 复制主包文件
        await copyMainPackageFiles(sourcePath, targetPath, platform);

        // 生成主包 manifest
        await generateMainPackageManifest(targetPath, platform, environment);

        Logger.success(`Main package packed to: ${targetPath}`);
    } catch (error) {
        exitWithError(`Main package packing failed: ${error.message}`);
    }
}

// Bundle 打包
async function packBundles(platform, environment, outputDir) {
    Logger.info(`Packing bundles for ${platform} (${environment})...`);

    try {
        const sourcePath = path.join(getPlatformBuildPath(platform), 'assets');
        const targetPath = outputDir || config.bundle_update.export_directory || path.join(projectPath, 'build', 'bundles', platform);

        // 确保目标目录存在
        if (!fs.existsSync(targetPath)) {
            fs.mkdirSync(targetPath, { recursive: true });
        }

        // 复制 Bundle 文件
        await copyBundleFiles(sourcePath, targetPath, platform);

        // 生成 Bundle manifest
        await generateBundleManifest(targetPath, platform, environment);

        Logger.success(`Bundles packed to: ${targetPath}`);
    } catch (error) {
        exitWithError(`Bundle packing failed: ${error.message}`);
    }
}

// 复制主包文件
async function copyMainPackageFiles(sourcePath, targetPath, platform) {
    Logger.verbose(`Copying main package files from ${sourcePath} to ${targetPath} for ${platform}`);

    if (!fs.existsSync(sourcePath)) {
        throw new Error(`Source path not found: ${sourcePath}`);
    }

    // 根据平台复制不同的文件
    if (platform === 'web-mobile') {
        // Web 平台复制 src 目录
        const srcSource = path.join(sourcePath, 'src');
        const srcTarget = path.join(targetPath, 'src');
        if (fs.existsSync(srcSource)) {
            await execCommand(`cp -r "${srcSource}" "${srcTarget}"`);
        }
    } else {
        // 原生平台复制整个构建目录的核心文件
        const coreFiles = ['assets', 'jsb-adapter', 'main.js'];
        for (const file of coreFiles) {
            const fileSource = path.join(sourcePath, file);
            const fileTarget = path.join(targetPath, file);
            if (fs.existsSync(fileSource)) {
                await execCommand(`cp -r "${fileSource}" "${fileTarget}"`);
                Logger.verbose(`Copied ${file} for ${platform}`);
            }
        }
    }

    // 复制主包资源
    const assetsSource = path.join(sourcePath, 'assets');
    const assetsTarget = path.join(targetPath, 'assets');
    if (fs.existsSync(assetsSource)) {
        // 只复制主包相关的资源目录
        const mainPackageAssets = ['internal', 'main', 'resources'];

        if (!fs.existsSync(assetsTarget)) {
            fs.mkdirSync(assetsTarget, { recursive: true });
        }

        for (const assetDir of mainPackageAssets) {
            const assetSource = path.join(assetsSource, assetDir);
            const assetTarget = path.join(assetsTarget, assetDir);
            if (fs.existsSync(assetSource)) {
                await execCommand(`cp -r "${assetSource}" "${assetTarget}"`);
                Logger.verbose(`Copied ${assetDir} directory`);
            }
        }

        // 根据配置复制包含的 bundles
        if (config.bundle_update && config.bundle_update.bundles) {
            for (const [bundleName, bundleConfig] of Object.entries(config.bundle_update.bundles)) {
                if (bundleConfig.include === true) {
                    const bundleSource = path.join(assetsSource, bundleName);
                    const bundleTarget = path.join(assetsTarget, bundleName);
                    if (fs.existsSync(bundleSource)) {
                        await execCommand(`cp -r "${bundleSource}" "${bundleTarget}"`);
                        Logger.verbose(`Copied included bundle: ${bundleName}`);
                    }
                }
            }
        }
    }
}

// 复制 Bundle 文件
async function copyBundleFiles(sourcePath, targetPath, platform) {
    Logger.verbose(`Copying bundle files from ${sourcePath} to ${targetPath} for ${platform}`);

    if (!fs.existsSync(sourcePath)) {
        throw new Error(`Source path not found: ${sourcePath}`);
    }

    // 复制所有 bundle 目录，但跳过主包资源
    const items = fs.readdirSync(sourcePath);
    const mainPackageAssets = ['internal', 'main', 'resources'];

    for (const item of items) {
        const itemPath = path.join(sourcePath, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory() && !mainPackageAssets.includes(item)) {
            // 检查是否在配置中启用
            if (config.bundle_update && config.bundle_update.bundles && config.bundle_update.bundles[item]) {
                const bundleConfig = config.bundle_update.bundles[item];
                if (bundleConfig.include !== false) { // 默认包含，除非明确设置为 false
                    const targetItemPath = path.join(targetPath, item);
                    await execCommand(`cp -r "${itemPath}" "${targetItemPath}"`);
                    Logger.verbose(`Copied bundle: ${item} for ${platform}`);
                }
            }
        }
    }
}

// 生成主包 manifest
async function generateMainPackageManifest(targetPath, platform, environment) {
    Logger.verbose(`Generating main package manifest for ${platform} (${environment})...`);

    try {
        const scriptPath = path.join(__dirname, '..', 'tools', 'main_package_generator.js');
        const version = config.main_package.version || '1.0.0';
        const serverAddress = getServerAddress('main_package', environment, platform);

        const args = [
            '-v', version,
            '-u', serverAddress,
            '-s', getPlatformBuildPath(platform),
            '-d', targetPath
        ];

        // 添加包含的 bundles 参数
        if (config.bundle_update && config.bundle_update.bundles) {
            const includedBundles = [];
            for (const [bundleName, bundleConfig] of Object.entries(config.bundle_update.bundles)) {
                if (bundleConfig.include === true) {
                    includedBundles.push(bundleName);
                }
            }
            if (includedBundles.length > 0) {
                args.push('-ib', JSON.stringify(includedBundles));
            }
        }

        await execCommand(`node "${scriptPath}" ${args.join(' ')}`);
        Logger.verbose(`Main package manifest generated for ${platform}`);
    } catch (error) {
        throw new Error(`Failed to generate main package manifest: ${error.message}`);
    }
}

// 生成 Bundle manifest
async function generateBundleManifest(targetPath, platform, environment) {
    Logger.verbose(`Generating bundle manifest for ${platform} (${environment})...`);

    try {
        const scriptPath = path.join(__dirname, '..', 'tools', 'version_generator.js');
        const version = config.bundle_update.version || '1.0.0';
        const serverAddress = getServerAddress('bundle_update', environment, platform);

        const args = [
            '-v', version,
            '-u', serverAddress,
            '-s', path.join(getPlatformBuildPath(platform), 'assets'),
            '-d', targetPath
        ];

        await execCommand(`node "${scriptPath}" ${args.join(' ')}`);
        Logger.verbose(`Bundle manifest generated for ${platform}`);
    } catch (error) {
        throw new Error(`Failed to generate bundle manifest: ${error.message}`);
    }
}

// 平台名称映射（用于服务器地址配置）
const PLATFORM_SERVER_MAP = {
    'web-mobile': 'WEB_MOBILE',
    'android': 'ANDROID',
    'ios': 'IOS'
};

// 获取服务器地址
function getServerAddress(updateType, environment = 'DEV', platform = 'web-mobile') {
    const serverPlatform = PLATFORM_SERVER_MAP[platform] || 'WEB_MOBILE';

    let serverAddresses;
    if (updateType === 'main_package') {
        serverAddresses = config.main_package.server_addresses;
    } else {
        serverAddresses = config.bundle_update.server_addresses;
    }

    if (serverAddresses && serverAddresses[environment] && serverAddresses[environment][serverPlatform]) {
        return serverAddresses[environment][serverPlatform];
    }

    // 降级到默认地址
    Logger.warn(`Server address not found for ${updateType}/${environment}/${serverPlatform}, using default`);
    return 'https://dev.example.com/h5/';
}

// 主函数
async function main() {
    const options = parseArguments();

    if (options.help) {
        showHelp();
        process.exit(0);
    }

    // 设置全局变量
    verbose = options.verbose;

    Logger.info('Bundle Tool Automated Build Script');
    Logger.info('=====================================');

    try {
        // 1. 验证环境
        validateEnvironment();

        // 2. 确定配置文件路径
        let configPath = options.config;
        if (!configPath && options.project) {
            configPath = path.join(__dirname, '..', 'config', `${options.project}.yaml`);
            Logger.info(`Using project config: ${configPath}`);
        }

        if (!configPath) {
            exitWithError('Configuration file or project name is required. Use --config <path> or --project <name>');
        }

        // 3. 加载配置
        config = loadConfig(configPath);

        // 4. 验证平台和环境参数
        const validPlatforms = ['web-mobile', 'android', 'ios'];
        const validEnvironments = ['DEV', 'STAGE', 'PROD'];

        if (!validPlatforms.includes(options.platform)) {
            exitWithError(`Invalid platform: ${options.platform}. Use: ${validPlatforms.join(', ')}`);
        }

        if (!validEnvironments.includes(options.environment)) {
            exitWithError(`Invalid environment: ${options.environment}. Use: ${validEnvironments.join(', ')}`);
        }

        Logger.info(`Building for platform: ${options.platform}`);
        Logger.info(`Environment: ${options.environment}`);
        Logger.info(`Target: ${options.target}`);
        if (options.skipBuild) {
            Logger.info('Skipping Cocos Creator build');
        }

        // 5. 执行构建和打包
        switch (options.target) {
            case 'main':
                await buildMainPackage(options.platform, options.environment, options.skipBuild);
                await packMainPackage(options.platform, options.environment, options.output);
                break;
            case 'bundle':
                await buildBundles(options.platform, options.environment, options.skipBuild);
                await packBundles(options.platform, options.environment, options.output);
                break;
            case 'all':
                await buildMainPackage(options.platform, options.environment, options.skipBuild);
                await packMainPackage(options.platform, options.environment, options.output);
                await packBundles(options.platform, options.environment, options.output);
                break;
            default:
                exitWithError(`Invalid target: ${options.target}. Use main, bundle, or all`);
        }

        Logger.success('Build and pack process completed successfully');
        Logger.info('=====================================');

    } catch (error) {
        exitWithError(`Build process failed: ${error.message}`);
    }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
    Logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    Logger.error(`Uncaught Exception: ${error.message}`);
    process.exit(1);
});

// 启动脚本
if (require.main === module) {
    main();
}

module.exports = {
    loadConfig,
    validateEnvironment,
    buildMainPackage,
    buildBundles
};
