# Bundle Tool 自动化构建脚本

## 概述

这是一个为 Bundle Tool 设计的命令行自动化构建脚本，专门用于 CI/CD 集成。脚本能够独立完成从配置读取到最终打包的全过程，无需 UI 界面交互。

## 功能特性

- ✅ **配置驱动**：所有构建行为由配置文件决定
- ✅ **逻辑复用**：调用现有的构建函数，不重复造轮子
- ✅ **健壮性**：完善的错误处理和退出机制
- ✅ **详细日志**：支持详细日志输出，便于 CI/CD 调试
- ✅ **模块化设计**：清晰的功能分离和代码结构

## 安装依赖

确保已安装必要的 npm 包：

```bash
cd packages/bundle-tool
npm install js-yaml
```

## 使用方法

### 基本用法

```bash
# 使用项目名称构建（推荐）
node scripts/build.js --project pkw-game --platform android --environment PROD

# 使用配置文件构建
node scripts/build.js --config ./config/pkw-game.yaml --platform ios --environment STAGE

# 快速构建（开发用）
node scripts/quick-build.js pkw-game --platform web-mobile --environment DEV

# 只构建主包
node scripts/build.js --project pkw-game --target main --platform android

# 只构建 Bundle
node scripts/build.js --project pkw-game --target bundle --platform ios

# 跳过 Cocos Creator 构建，直接打包
node scripts/build.js --project pkw-game --skip-build --target bundle

# 使用 npm scripts（最简单）
npm run build:pkw:android:prod
```

### 命令行参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--config` | string | ❌ | - | 配置文件路径（可通过 --project 自动推导） |
| `--project` | string | ❌ | - | 项目名称（如：pkw-game, wpk-game） |
| `--platform` | string | ❌ | `web-mobile` | 目标平台：`web-mobile`、`android`、`ios` |
| `--environment` | string | ❌ | `DEV` | 环境：`DEV`、`STAGE`、`PROD` |
| `--target` | string | ❌ | `all` | 构建目标：`main`、`bundle`、`all` |
| `--output` | string | ❌ | 配置文件中的路径 | 输出目录 |
| `--skip-build` | boolean | ❌ | `false` | 跳过 Cocos Creator 构建 |
| `--verbose` | boolean | ❌ | `false` | 启用详细日志 |
| `--help` | boolean | ❌ | `false` | 显示帮助信息 |

## 配置文件格式

配置文件使用 YAML 格式，与 UI 编辑器中的配置结构完全一致。

### 完整配置示例

```yaml
# 主包更新配置
main_package:
  version: "1.0.0"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/h5/"
    PROD:
      WEB_MOBILE: "https://prod.example.com/h5/"
  export_directory: "./build/main-package"

# Bundle更新配置
bundle_update:
  version: "1.0.0"
  export_directory: "./build/bundles"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/h5/"
    PROD:
      WEB_MOBILE: "https://prod.example.com/h5/"
  bundles:
    common:
      version: "1.0"
      include: true  # 包含在主包中
      dependencies: []
    game-core:
      version: "1.0"
      include: false # 作为独立 bundle
      dependencies: ["common"]
```

## CI/CD 集成

### GitHub Actions 示例

```yaml
name: Build Bundle Tool

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: |
        cd packages/bundle-tool
        npm install
        
    - name: Build bundles
      run: |
        cd packages/bundle-tool
        node scripts/build.js --config ./config/pkw-game.yaml --verbose
        
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: bundle-artifacts
        path: |
          build/main-package/
          build/bundles/
```

### Jenkins Pipeline 示例

```groovy
pipeline {
    agent any
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Install Dependencies') {
            steps {
                dir('packages/bundle-tool') {
                    sh 'npm install'
                }
            }
        }
        
        stage('Build') {
            steps {
                dir('packages/bundle-tool') {
                    sh 'node scripts/build.js --config ./config/pkw-game.yaml --verbose'
                }
            }
        }
        
        stage('Archive') {
            steps {
                archiveArtifacts artifacts: 'build/**/*', fingerprint: true
            }
        }
    }
    
    post {
        failure {
            echo 'Build failed!'
        }
        success {
            echo 'Build succeeded!'
        }
    }
}
```

## 构建流程

脚本执行以下步骤：

1. **环境验证**
   - 检查项目目录结构
   - 验证 Cocos Creator 环境配置

2. **配置加载**
   - 解析 YAML 配置文件
   - 验证配置结构完整性

3. **执行构建**
   - 调用 Cocos Creator 构建命令
   - 根据目标类型执行相应构建

4. **分类打包**
   - 复制构建产物到指定目录
   - 生成相应的 manifest 文件
   - 根据配置处理 bundle 包含关系

## 错误处理

脚本具有完善的错误处理机制：

- **配置错误**：配置文件不存在或格式错误时立即退出
- **环境错误**：Cocos Creator 路径不正确时报错退出
- **构建错误**：构建过程失败时输出详细错误信息并退出
- **文件错误**：文件复制或 manifest 生成失败时报错退出

所有错误都会以非零退出码结束，确保 CI/CD 系统能正确识别失败状态。

## 日志输出

脚本提供多级别的日志输出：

- `[INFO]`：基本信息
- `[WARN]`：警告信息
- `[ERROR]`：错误信息
- `[SUCCESS]`：成功信息
- `[VERBOSE]`：详细调试信息（需要 `--verbose` 参数）

## 故障排除

### 常见问题

1. **配置文件找不到**
   ```
   [ERROR] Configuration file not found: ./config/pkw-game.yaml
   ```
   解决：检查配置文件路径是否正确

2. **Cocos Creator 环境未配置**
   ```
   [ERROR] Cocos Creator environment not configured
   ```
   解决：确保 `temp/env_variables.json` 文件存在且配置正确

3. **构建失败**
   ```
   [ERROR] Main package build failed
   ```
   解决：检查 Cocos Creator 路径和项目配置

### 调试技巧

- 使用 `--verbose` 参数查看详细日志
- 检查 `temp/env_variables.json` 文件内容
- 确认配置文件格式正确（可使用在线 YAML 验证器）
- 手动执行构建命令验证环境

## 扩展开发

脚本采用模块化设计，可以轻松扩展：

- 添加新的构建目标
- 支持更多平台
- 集成额外的后处理步骤
- 添加自定义验证逻辑

主要函数都已导出，可以在其他脚本中复用：

```javascript
const { loadConfig, validateEnvironment, buildMainPackage, buildBundles } = require('./build.js');
```
