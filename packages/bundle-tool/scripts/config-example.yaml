# Bundle Tool 自动化构建配置示例
# 此文件展示了完整的配置结构，用于命令行构建脚本

# 主包更新配置
main_package:
  version: "1.0.0"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/h5/"
      IOS: "https://dev.example.com/ios/"
      ANDROID: "https://dev.example.com/android/"
    STAGE:
      WEB_MOBILE: "https://stage.example.com/h5/"
      IOS: "https://stage.example.com/ios/"
      ANDROID: "https://stage.example.com/android/"
    PROD:
      WEB_MOBILE: "https://prod.example.com/h5/"
      IOS: "https://prod.example.com/ios/"
      ANDROID: "https://prod.example.com/android/"
  export_directory: "./build/main-package"

# Bundle更新配置
bundle_update:
  version: "1.0.0"
  export_directory: "./build/bundles"
  server_addresses:
    DEV:
      WEB_MOBILE: "https://dev.example.com/h5/"
      IOS: "https://dev.example.com/ios/"
      ANDROID: "https://dev.example.com/android/"
    STAGE:
      WEB_MOBILE: "https://stage.example.com/h5/"
      IOS: "https://stage.example.com/ios/"
      ANDROID: "https://stage.example.com/android/"
    PROD:
      WEB_MOBILE: "https://prod.example.com/h5/"
      IOS: "https://prod.example.com/ios/"
      ANDROID: "https://prod.example.com/android/"
  bundles:
    common:
      version: "1.0"
      md5: ""
      url: ""
      include: true  # 是否包含在主包中
      dependencies: []
    lobby-common:
      version: "1.0"
      md5: ""
      url: ""
      include: true
      dependencies: ["common"]
    game-core:
      version: "1.0"
      md5: ""
      url: ""
      include: false  # 不包含在主包中，作为独立 bundle
      dependencies: ["common", "lobby-common"]
    ui-framework:
      version: "1.0"
      md5: ""
      url: ""
      include: true
      dependencies: ["common"]
    audio-manager:
      version: "1.0"
      md5: ""
      url: ""
      include: false
      dependencies: []
