#!/usr/bin/env node

/**
 * Bundle Tool 快速构建脚本
 * 
 * 简化版本，用于快速测试和开发
 * 
 * 用法:
 * node scripts/quick-build.js pkw-game
 * node scripts/quick-build.js pkw-game --main-only
 * node scripts/quick-build.js pkw-game --bundle-only
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { exec } = require('child_process');

// 简单的日志工具
const log = {
    info: (msg) => console.log(`ℹ️  ${msg}`),
    success: (msg) => console.log(`✅ ${msg}`),
    error: (msg) => console.error(`❌ ${msg}`),
    warn: (msg) => console.warn(`⚠️  ${msg}`)
};

// 获取项目配置路径
function getConfigPath(projectName) {
    return path.join(__dirname, '..', 'config', `${projectName}.yaml`);
}

// 加载配置
function loadConfig(projectName) {
    const configPath = getConfigPath(projectName);
    
    if (!fs.existsSync(configPath)) {
        log.error(`配置文件不存在: ${configPath}`);
        process.exit(1);
    }
    
    try {
        const content = fs.readFileSync(configPath, 'utf-8');
        return yaml.load(content);
    } catch (error) {
        log.error(`配置文件解析失败: ${error.message}`);
        process.exit(1);
    }
}

// 执行命令
function execCommand(command, description) {
    return new Promise((resolve, reject) => {
        log.info(`${description}...`);
        
        exec(command, { cwd: process.cwd() }, (error, stdout, stderr) => {
            if (error) {
                log.error(`${description}失败: ${error.message}`);
                if (stderr) log.error(`错误输出: ${stderr}`);
                reject(error);
            } else {
                log.success(`${description}完成`);
                resolve({ stdout, stderr });
            }
        });
    });
}

// 获取构建命令
function getBuildCommand() {
    const envPath = path.join(process.cwd(), 'temp', 'env_variables.json');
    
    if (!fs.existsSync(envPath)) {
        log.error('Cocos Creator 环境未配置，请先在编辑器中构建一次');
        process.exit(1);
    }
    
    const envConfig = JSON.parse(fs.readFileSync(envPath, 'utf-8'));
    
    if (!envConfig.cocosCreatorPath) {
        log.error('Cocos Creator 路径未配置');
        process.exit(1);
    }
    
    const buildOptions = {
        platform: 'web-mobile',
        debug: false,
        md5Cache: true,
        nativeBuildTemplate: 'link'
    };
    
    return `"${envConfig.cocosCreatorPath}" --path "${process.cwd()}" --build "${JSON.stringify(buildOptions)}"`;
}

// 复制文件
async function copyFiles(source, target, description) {
    if (!fs.existsSync(source)) {
        log.warn(`源目录不存在: ${source}`);
        return;
    }
    
    // 确保目标目录存在
    const targetDir = path.dirname(target);
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
    }
    
    await execCommand(`cp -r "${source}" "${target}"`, description);
}

// 主包构建和打包
async function buildMainPackage(config) {
    log.info('🏗️  开始主包构建流程');
    
    // 1. 构建项目
    const buildCommand = getBuildCommand();
    await execCommand(buildCommand, '执行 Cocos Creator 构建');
    
    // 2. 复制主包文件
    const sourcePath = path.join(process.cwd(), 'build', 'web-mobile');
    const targetPath = config.main_package.export_directory || path.join(process.cwd(), 'build', 'main-package');
    
    // 复制 src 目录
    const srcSource = path.join(sourcePath, 'src');
    const srcTarget = path.join(targetPath, 'src');
    await copyFiles(srcSource, srcTarget, '复制 src 目录');
    
    // 复制主包资源
    const assetsSource = path.join(sourcePath, 'assets');
    const assetsTarget = path.join(targetPath, 'assets');
    
    if (fs.existsSync(assetsSource)) {
        if (!fs.existsSync(assetsTarget)) {
            fs.mkdirSync(assetsTarget, { recursive: true });
        }
        
        // 复制主包资源目录
        const mainAssets = ['internal', 'main', 'resources'];
        for (const asset of mainAssets) {
            const assetSource = path.join(assetsSource, asset);
            const assetTarget = path.join(assetsTarget, asset);
            if (fs.existsSync(assetSource)) {
                await copyFiles(assetSource, assetTarget, `复制 ${asset} 目录`);
            }
        }
        
        // 复制包含的 bundles
        if (config.bundle_update && config.bundle_update.bundles) {
            for (const [bundleName, bundleConfig] of Object.entries(config.bundle_update.bundles)) {
                if (bundleConfig.include === true) {
                    const bundleSource = path.join(assetsSource, bundleName);
                    const bundleTarget = path.join(assetsTarget, bundleName);
                    if (fs.existsSync(bundleSource)) {
                        await copyFiles(bundleSource, bundleTarget, `复制包含的 bundle: ${bundleName}`);
                    }
                }
            }
        }
    }
    
    // 3. 生成 manifest
    const manifestScript = path.join(__dirname, '..', 'tools', 'main_package_generator.js');
    if (fs.existsSync(manifestScript)) {
        const version = config.main_package.version || '1.0.0';
        const serverAddress = getServerAddress(config, 'main_package');
        
        const manifestArgs = [
            '-v', version,
            '-u', serverAddress,
            '-s', sourcePath,
            '-d', targetPath
        ];
        
        await execCommand(`node "${manifestScript}" ${manifestArgs.join(' ')}`, '生成主包 manifest');
    }
    
    log.success(`🎉 主包构建完成: ${targetPath}`);
}

// Bundle 构建和打包
async function buildBundles(config) {
    log.info('📦 开始 Bundle 构建流程');
    
    // 1. 构建项目（如果还没构建过）
    const buildPath = path.join(process.cwd(), 'build', 'web-mobile');
    if (!fs.existsSync(buildPath)) {
        const buildCommand = getBuildCommand();
        await execCommand(buildCommand, '执行 Cocos Creator 构建');
    }
    
    // 2. 复制 Bundle 文件
    const sourcePath = path.join(buildPath, 'assets');
    const targetPath = config.bundle_update.export_directory || path.join(process.cwd(), 'build', 'bundles');
    
    if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true });
    }
    
    // 复制 bundle 目录
    if (fs.existsSync(sourcePath)) {
        const items = fs.readdirSync(sourcePath);
        const mainAssets = ['internal', 'main', 'resources'];
        
        for (const item of items) {
            const itemPath = path.join(sourcePath, item);
            const stat = fs.statSync(itemPath);
            
            if (stat.isDirectory() && !mainAssets.includes(item)) {
                // 检查是否应该包含这个 bundle
                if (config.bundle_update && config.bundle_update.bundles && config.bundle_update.bundles[item]) {
                    const bundleConfig = config.bundle_update.bundles[item];
                    if (bundleConfig.include !== false) {
                        const targetItemPath = path.join(targetPath, item);
                        await copyFiles(itemPath, targetItemPath, `复制 bundle: ${item}`);
                    }
                }
            }
        }
    }
    
    log.success(`🎉 Bundle 构建完成: ${targetPath}`);
}

// 获取服务器地址
function getServerAddress(config, type) {
    const env = process.env.NODE_ENV || 'DEV';
    const platform = 'WEB_MOBILE';
    
    let addresses;
    if (type === 'main_package') {
        addresses = config.main_package.server_addresses;
    } else {
        addresses = config.bundle_update.server_addresses;
    }
    
    if (addresses && addresses[env] && addresses[env][platform]) {
        return addresses[env][platform];
    }
    
    return 'https://dev.example.com/h5/';
}

// 显示帮助
function showHelp() {
    console.log(`
Bundle Tool 快速构建脚本

用法:
  node scripts/quick-build.js <project-name> [options]

参数:
  project-name     项目名称 (如: pkw-game, wpk-game)
  --main-only      只构建主包
  --bundle-only    只构建 Bundle
  --help           显示帮助信息

示例:
  node scripts/quick-build.js pkw-game
  node scripts/quick-build.js pkw-game --main-only
  node scripts/quick-build.js pkw-game --bundle-only
`);
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.length === 0) {
        showHelp();
        process.exit(0);
    }
    
    const projectName = args[0];
    const mainOnly = args.includes('--main-only');
    const bundleOnly = args.includes('--bundle-only');
    
    if (!projectName) {
        log.error('请指定项目名称');
        showHelp();
        process.exit(1);
    }
    
    try {
        log.info(`🚀 开始构建项目: ${projectName}`);
        
        const config = loadConfig(projectName);
        
        if (bundleOnly) {
            await buildBundles(config);
        } else if (mainOnly) {
            await buildMainPackage(config);
        } else {
            await buildMainPackage(config);
            await buildBundles(config);
        }
        
        log.success('🎊 所有构建任务完成！');
        
    } catch (error) {
        log.error(`构建失败: ${error.message}`);
        process.exit(1);
    }
}

// 启动脚本
if (require.main === module) {
    main();
}
