#!/usr/bin/env node

/**
 * Bundle Tool Quick Build Script
 * 
 * Simplified version for fast testing and development
 * 
 * Usage:
 * node scripts/quick-build.js pkw-game
 * node scripts/quick-build.js pkw-game --main-only
 * node scripts/quick-build.js pkw-game --bundle-only
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { exec } = require('child_process');
const dotenv = require('dotenv');

// Simple logger
const log = {
    info: (msg) => console.log(`ℹ️  ${msg}`),
    success: (msg) => console.log(`✅ ${msg}`),
    error: (msg) => console.error(`❌ ${msg}`),
    warn: (msg) => console.warn(`⚠️  ${msg}`)
};

// Get the grandparent directory of the current directory
const grandParentDir = path.resolve(__dirname, '../../..');

// Get project config path
function getConfigPath(projectName) {
    return path.join(__dirname, '..', 'config', `${projectName}.yaml`);
}

// Load config
function loadConfig(projectName) {
    const configPath = getConfigPath(projectName);
    
    if (!fs.existsSync(configPath)) {
        log.error(`Config file does not exist: ${configPath}`);
        process.exit(1);
    }
    
    try {
        const content = fs.readFileSync(configPath, 'utf-8');
        return yaml.load(content);
    } catch (error) {
        log.error(`Failed to parse config file: ${error.message}`);
        process.exit(1);
    }
}

// Execute command
function execCommand(command, description) {
    return new Promise((resolve, reject) => {
        log.info('buildCommand:',command);
        log.info(`${description}...`);
        
        exec(command, { cwd: grandParentDir }, (error, stdout, stderr) => {
            if (error) {
                log.error(`${description} failed: ${error.message}`);
                if (stderr) log.error(`Error output: ${stderr}`);
                reject(error);
            } else {
                log.success(`${description} completed`);
                resolve({ stdout, stderr });
            }
        });
    });
}

// Read .env file (grandparent directory)
function getCocosCreatorPathFromEnv() {
    const envPath = path.resolve(__dirname, '../../../.env');
    console.log(envPath);
    if (fs.existsSync(envPath)) {
        const envConfig = dotenv.parse(fs.readFileSync(envPath));
        if (envConfig.cocosCreatorPath) {
            return envConfig.cocosCreatorPath;
        }
    }
    // Default command name
    return 'CocosCreator';
}

// Get build command
function getBuildCommand(platform = 'web-mobile', buildTemplate = 'link') {
    const cocosCreatorPath = getCocosCreatorPathFromEnv();
    // Build options
    const buildOptions = {
        platform: platform,
        debug: false,
        md5Cache: true
    };

    // Only native platforms need nativeBuildTemplate
    if (platform === 'android' || platform === 'ios') {
        buildOptions.nativeBuildTemplate = buildTemplate;
    }

    const buildString = JSON.stringify(buildOptions);
    log.info(`Build options: ${buildString}`);

    // Use the given Cocos Creator path or default command
    return `"${cocosCreatorPath}" --path "${grandParentDir}" --build '${buildString}'`;
}

// Copy files
async function copyFiles(source, target, description) {
    if (!fs.existsSync(source)) {
        log.warn(`Source directory does not exist: ${source}`);
        return;
    }
    
    // Ensure target directory exists
    const targetDir = path.dirname(target);
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
    }
    
    await execCommand(`cp -r "${source}" "${target}"`, description);
}

// Execute Cocos Creator build (only once)
async function buildCocosProject(platform, buildTemplate, skipBuild) {
    if (skipBuild) {
        log.info('⏭️  Skip Cocos Creator build');
        return;
    }

    log.info(`🏗️  Execute Cocos Creator build (${platform}, ${buildTemplate})`);
    const buildCommand = getBuildCommand(platform, buildTemplate);
    await execCommand(buildCommand, 'Execute Cocos Creator build');
}

// Get platform build path
function getPlatformBuildPath(platform) {
    return path.join(grandParentDir, 'build', platform);
}

// Main package build and pack
async function buildMainPackage(config, platform = 'web-mobile', environment = 'DEV', skipBuild = false, buildTemplate = 'link') {
    log.info('📦 Start main package build process');

    // 1. Get build path
    const sourcePath = getPlatformBuildPath(platform);
    const targetPath = config.main_package.export_directory 
  ? path.resolve(grandParentDir, config.main_package.export_directory) 
  : path.join(grandParentDir, 'build', 'main-package');

    // 2. Copy main package files
    if (platform === 'web-mobile') {
        // For web platform, copy src directory
        const srcSource = path.join(sourcePath, 'src');
        const srcTarget = path.join(targetPath, 'src');
        await copyFiles(srcSource, srcTarget, 'Copy src directory');
    }

    // 3. Copy main package assets
    const assetsSource = path.join(sourcePath, 'assets');
    const assetsTarget = path.join(targetPath, 'assets');

    if (fs.existsSync(assetsSource)) {
        if (!fs.existsSync(assetsTarget)) {
            fs.mkdirSync(assetsTarget, { recursive: true });
        }

        // Copy main package asset directories
        const mainAssets = ['internal', 'main', 'resources'];
        for (const asset of mainAssets) {
            const assetSource = path.join(assetsSource, asset);
            const assetTarget = path.join(assetsTarget, asset);
            if (fs.existsSync(assetSource)) {
                await copyFiles(assetSource, assetTarget, `Copy ${asset} directory`);
            }
        }

        // Copy included bundles
        if (config.bundle_update && config.bundle_update.bundles) {
            for (const [bundleName, bundleConfig] of Object.entries(config.bundle_update.bundles)) {
                if (bundleConfig.include === true) {
                    const bundleSource = path.join(assetsSource, bundleName);
                    const bundleTarget = path.join(assetsTarget, bundleName);
                    if (fs.existsSync(bundleSource)) {
                        await copyFiles(bundleSource, bundleTarget, `Copy included bundle: ${bundleName}`);
                    }
                }
            }
        }
    }

    // 4. Generate manifest
    const manifestScript = path.join(__dirname, '..', 'tools', 'main_package_generator.js');
    if (fs.existsSync(manifestScript)) {
        const version = config.main_package.version || '1.0.0';
        const serverAddress = getServerAddress(config, 'main_package', environment, platform);

        const manifestArgs = [
            '-v', version,
            '-u', serverAddress,
            '-s', sourcePath,
            '-d', targetPath
        ];

        await execCommand(`node "${manifestScript}" ${manifestArgs.join(' ')}`, 'Generate main package manifest');
    }

    log.success(`🎉 Main package build completed: ${targetPath}`);
}

// Bundle build and pack
async function buildBundles(config, platform = 'web-mobile', environment = 'DEV', skipBuild = false, buildTemplate = 'link') {
    log.info('📦 Start bundle build process');

    // 1. Get build path
    const buildPath = getPlatformBuildPath(platform);
    const sourcePath = path.join(buildPath, 'assets');
    const targetPath = config.bundle_update.export_directory 
  ? path.resolve(grandParentDir, config.bundle_update.export_directory) 
  : path.join(grandParentDir, 'build', 'bundles');

    if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true });
    }

    // 2. Copy bundle files
    if (fs.existsSync(sourcePath)) {
        const items = fs.readdirSync(sourcePath);
        const mainAssets = ['internal', 'main', 'resources'];

        for (const item of items) {
            const itemPath = path.join(sourcePath, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory() && !mainAssets.includes(item)) {
                // Check if this bundle should be included
                if (config.bundle_update && config.bundle_update.bundles && config.bundle_update.bundles[item]) {
                    const bundleConfig = config.bundle_update.bundles[item];
                    if (bundleConfig.include !== false) {
                        const targetItemPath = path.join(targetPath, item);
                        await copyFiles(itemPath, targetItemPath, `Copy bundle: ${item}`);
                    }
                }
            }
        }
    }

    log.success(`🎉 Bundle build completed: ${targetPath}`);
}

// Get server address
function getServerAddress(config, type, environment = 'DEV', platform = 'web-mobile') {
    // Platform name mapping
    const platformMap = {
        'web-mobile': 'WEB_MOBILE',
        'android': 'ANDROID',
        'ios': 'IOS'
    };

    const serverPlatform = platformMap[platform] || 'WEB_MOBILE';

    let addresses;
    if (type === 'main_package') {
        addresses = config.main_package.server_addresses;
    } else {
        addresses = config.bundle_update.server_addresses;
    }

    if (addresses && addresses[environment] && addresses[environment][serverPlatform]) {
        return addresses[environment][serverPlatform];
    }

    return 'https://dev.example.com/h5/';
}

// Show help
function showHelp() {
    console.log(`
Bundle Tool Quick Build Script

Usage:
  node scripts/quick-build.js <project-name> [options]

Options:
  project-name     Project name (e.g.: pkw-game, wpk-game)
  --platform       Target platform: web-mobile|android|ios (default: web-mobile)
  --environment    Environment: DEV|STAGE|PROD (default: DEV)
  --template       Build mode: link|default (default: link, only for native platforms)
  --main-only      Only build main package
  --bundle-only    Only build bundles
  --skip-build     Skip Cocos Creator build
  --help           Show help info

Examples:
  node scripts/quick-build.js pkw-game
  node scripts/quick-build.js pkw-game --platform android --environment PROD
  node scripts/quick-build.js pkw-game --platform ios --template default
  node scripts/quick-build.js pkw-game --main-only --platform android --template link
  node scripts/quick-build.js pkw-game --bundle-only --skip-build
`);
}

// Main function
async function main() {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.length === 0) {
        showHelp();
        process.exit(0);
    }

    const projectName = args[0];
    const platform = args.includes('--platform') ? args[args.indexOf('--platform') + 1] : 'web-mobile';
    const environment = args.includes('--environment') ? args[args.indexOf('--environment') + 1] : 'DEV';
    const buildTemplate = args.includes('--template') ? args[args.indexOf('--template') + 1] : 'link';
    const mainOnly = args.includes('--main-only');
    const bundleOnly = args.includes('--bundle-only');
    const skipBuild = args.includes('--skip-build');

    if (!projectName) {
        log.error('Please specify project name');
        showHelp();
        process.exit(1);
    }

    // Validate parameters
    const validPlatforms = ['web-mobile', 'android', 'ios'];
    const validEnvironments = ['DEV', 'STAGE', 'PROD'];
    const validTemplates = ['link', 'default'];

    if (!validPlatforms.includes(platform)) {
        log.error(`Invalid platform: ${platform}. Supported platforms: ${validPlatforms.join(', ')}`);
        process.exit(1);
    }

    if (!validEnvironments.includes(environment)) {
        log.error(`Invalid environment: ${environment}. Supported environments: ${validEnvironments.join(', ')}`);
        process.exit(1);
    }

    if (!validTemplates.includes(buildTemplate)) {
        log.error(`Invalid build mode: ${buildTemplate}. Supported modes: ${validTemplates.join(', ')}`);
        process.exit(1);
    }

    try {
        log.info(`🚀 Start building project: ${projectName}`);
        log.info(`📱 Platform: ${platform}`);
        log.info(`🌍 Environment: ${environment}`);
        if (platform === 'android' || platform === 'ios') {
            log.info(`🔧 Build mode: ${buildTemplate}`);
        }
        if (skipBuild) {
            log.info('⏭️  Skip Cocos Creator build');
        }

        const config = loadConfig(projectName);

        // 1. Execute Cocos Creator build (only once)
        await buildCocosProject(platform, buildTemplate, skipBuild);

        // 2. Build according to target
        if (bundleOnly) {
            await buildBundles(config, platform, environment, skipBuild, buildTemplate);
        } else if (mainOnly) {
            await buildMainPackage(config, platform, environment, skipBuild, buildTemplate);
        } else {
            await buildMainPackage(config, platform, environment, skipBuild, buildTemplate);
            await buildBundles(config, platform, environment, skipBuild, buildTemplate);
        }

        log.success('🎊 All build tasks completed!');

    } catch (error) {
        log.error(`Build failed: ${error.message}`);
        process.exit(1);
    }
}

// Start script
if (require.main === module) {
    main();
}
