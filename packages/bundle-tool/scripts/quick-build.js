#!/usr/bin/env node

/**
 * Bundle Tool Quick Build Script
 * 
 * Simplified version for fast testing and development
 * 
 * Usage:
 * node scripts/quick-build.js pkw-game
 * node scripts/quick-build.js pkw-game --main-only
 * node scripts/quick-build.js pkw-game --bundle-only
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { exec } = require('child_process');
const { buildProject } = require('../dist/pack-bundle/helper');
const ContextConfig = require('../dist/pack-bundle/context').default;

// Import required constants for helper.buildProject
const DevicePlatform = {
    WEB_MOBILE: 'web-mobile',
    ANDROID: 'android',
    IOS: 'ios'
};

const CocosNativeBuildTemplate = {
    LINK: 'link',
    DEFAULT: 'default'
};
// Simple logger
const log = {
    info: (msg) => console.log(`ℹ️  ${msg}`),
    success: (msg) => console.log(`✅ ${msg}`),
    error: (msg) => console.error(`❌ ${msg}`),
    warn: (msg) => console.warn(`⚠️  ${msg}`),
    log: (msg) => console.log(msg)  // Add log method for helper.buildProject compatibility
};

// Get the grandparent directory of the current directory
const grandParentDir = path.resolve(__dirname, '../../..');

// Get project config path
function getConfigPath(projectName) {
    return path.join(__dirname, '..', 'config', `${projectName}.yaml`);
}

// Load config
function loadConfig(projectName) {
    const configPath = getConfigPath(projectName);
    
    if (!fs.existsSync(configPath)) {
        log.error(`Config file does not exist: ${configPath}`);
        process.exit(1);
    }
    
    try {
        const content = fs.readFileSync(configPath, 'utf-8');
        return yaml.load(content);
    } catch (error) {
        log.error(`Failed to parse config file: ${error.message}`);
        process.exit(1);
    }
}

function loadUIState() {
    const uiStatePath = path.join(grandParentDir, 'temp', 'bundle-ui-state.json');

    if (!fs.existsSync(uiStatePath)) {
        log.warn('UI state file not found, using config file settings');
        return null;
    }

    try {
        const uiStateContent = fs.readFileSync(uiStatePath, 'utf8');
        const uiState = JSON.parse(uiStateContent);
        log.info(`Loaded UI state from: ${uiStatePath}`);
        log.info(`Bundle include states: ${JSON.stringify(uiState.bundleIncludeStates)}`);
        return uiState;
    } catch (error) {
        log.warn(`Failed to load UI state: ${error.message}, using config file settings`);
        return null;
    }
}

// Execute command
function execCommand(command, description) {
    return new Promise((resolve, reject) => {
        log.info(`${description}...`);

        exec(command, { cwd: grandParentDir }, (error, stdout, stderr) => {
            if (error) {
                log.error(`${description} failed: ${error.message}`);
                if (stderr) log.error(`Error output: ${stderr}`);
                reject(error);
            } else {
                log.success(`${description} completed`);
                resolve({ stdout, stderr });
            }
        });
    });
}

// Execute command with arguments (for better JSON handling)
function execCommandWithArgs(command, args, description) {
    return new Promise((resolve, reject) => {
        const { spawn } = require('child_process');

        log.info(`${description}...`);

        const child = spawn(command, args, { cwd: grandParentDir });
        let stdout = '';
        let stderr = '';

        child.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        child.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        child.on('close', (code) => {
            if (code !== 0) {
                log.error(`${description} failed with code ${code}`);
                if (stderr) {
                    log.error(`Error output: ${stderr}`);
                }
                reject(new Error(`Command failed with code ${code}`));
                return;
            }

            if (stderr) {
                log.warn(`Warning: ${stderr}`);
            }

            log.success(`${description} completed`);
            resolve({ stdout, stderr });
        });
    });
}



// Copy files
async function copyFiles(source, target, description) {
    if (!fs.existsSync(source)) {
        log.warn(`Source directory does not exist: ${source}`);
        return;
    }

    // Ensure target directory exists
    const targetDir = path.dirname(target);
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
    }

    await execCommand(`cp -r "${source}" "${target}"`, description);
}

async function buildCocosProject(config, platform, buildTemplate, skipBuild) {
    if (skipBuild) {
        log.info('⏭️  Skip Cocos Creator build');
        return;
    }

    log.info(`🏗️  Execute Cocos Creator build using helper.buildProject (${platform}, ${buildTemplate})`);

    return new Promise((resolve, reject) => {
        const env = {
            console: log,
            context: ContextConfig.get(grandParentDir)
        };

        // Get project name from config (fallback to 'PKW' if not found)
        const projectName = config.project_name || 'PKW';

        buildProject(
            env,
            grandParentDir,
            projectName,
            platform,
            buildTemplate,
            (info, error) => {
                if (error) {
                    log.error(`Build failed: ${info}`);
                    reject(new Error(info));
                } else {
                    log.success(`Build completed: ${info}`);
                    resolve();
                }
            }
        );
    });
}

// Get platform build path
function getPlatformBuildPath(platform, buildTemplate = 'link') {
    // For web-mobile, check which directory actually exists
    if (platform === 'web-mobile') {
        const webMobilePath = path.join(grandParentDir, 'build', 'web-mobile');
        const jsbLinkPath = path.join(grandParentDir, 'build', 'jsb-link');
        const jsbDefaultPath = path.join(grandParentDir, 'build', 'jsb-default');

        // Check in order of preference
        if (fs.existsSync(webMobilePath)) {
            return webMobilePath;
        } else if (fs.existsSync(jsbLinkPath)) {
            return jsbLinkPath;
        } else if (fs.existsSync(jsbDefaultPath)) {
            return jsbDefaultPath;
        } else {
            // Fallback to expected path
            return webMobilePath;
        }
    }

    // For native platforms (android/ios), use template to determine build path
    const buildPlatform = buildTemplate === 'link' ? 'jsb-link' : 'jsb-default';
    return path.join(grandParentDir, 'build', buildPlatform);
}

async function buildMainPackage(config, platform = 'web-mobile', environment = 'DEV', buildTemplate = 'link') {
    log.info('📦 Start main package build process');

    const sourcePath = getPlatformBuildPath(platform, buildTemplate);
    const targetPath = config.main_package.export_directory
        ? path.resolve(grandParentDir, config.main_package.export_directory)
        : path.join(grandParentDir, 'build', 'main-package');

    // 2. Copy src main package files
    const srcSource = path.join(sourcePath, 'src');
    const srcTarget = path.join(targetPath, 'src');
    await copyFiles(srcSource, srcTarget, 'Copy src directory');
    

    // 3. Copy main package assets
    const assetsSource = path.join(sourcePath, 'assets');
    const assetsTarget = path.join(targetPath, 'assets');

    if (fs.existsSync(assetsSource)) {
        if (!fs.existsSync(assetsTarget)) {
            fs.mkdirSync(assetsTarget, { recursive: true });
        }

        // Copy main package asset directories
        const mainAssets = ['internal', 'main', 'resources'];
        for (const asset of mainAssets) {
            const assetSource = path.join(assetsSource, asset);
            const assetTarget = path.join(assetsTarget, asset);
            if (fs.existsSync(assetSource)) {
                await copyFiles(assetSource, assetTarget, `Copy ${asset} directory`);
            }
        }

        // Copy bundles based on UI state or config
        const uiState = loadUIState();
        const bundleDirs = fs.readdirSync(assetsSource)
            .filter(name => fs.statSync(path.join(assetsSource, name)).isDirectory());

        for (const bundleName of bundleDirs) {
            let shouldInclude = false;

            if (uiState && uiState.bundleIncludeStates) {
                // Use UI state if available
                shouldInclude = uiState.bundleIncludeStates[bundleName] === true;
            } else if (config.bundle_update && config.bundle_update.bundles) {
                // Fallback to config file
                const bundleConfig = config.bundle_update.bundles[bundleName];
                shouldInclude = bundleConfig && bundleConfig.include === true;
            }

            if (shouldInclude) {
                // Skip bundles marked as included (they should only be in bundles folder)
                log.info(`⏭️  Skipped bundle: ${bundleName} (marked as included, will be in bundles folder only)`);
                continue;
            }

            // Copy bundles not marked as included to main package
            const bundleSource = path.join(assetsSource, bundleName);
            const bundleTarget = path.join(assetsTarget, bundleName);
            await copyFiles(bundleSource, bundleTarget, `Copy bundle to main package: ${bundleName}`);
        }
    }

    // 4. Generate manifest
    const manifestScript = path.join(__dirname, '..', 'tools', 'main_package_generator.js');
    if (fs.existsSync(manifestScript)) {
        const version = config.main_package.version || '1.0.0';
        const serverAddress = getServerAddress(config, 'main_package', environment, platform);

        const manifestArgs = [
            '-v', version,
            '-u', serverAddress,
            '-s', sourcePath,
            '-d', targetPath
        ];

        // Determine excluded bundles based on UI state or config
        const excludedBundles = [];
        const uiState = loadUIState();

        if (uiState && uiState.bundleIncludeStates) {
            // Use UI state if available
            for (const [bundleName, isIncluded] of Object.entries(uiState.bundleIncludeStates)) {
                if (isIncluded === true) {
                    excludedBundles.push(bundleName);
                }
            }
        } else if (config.bundle_update && config.bundle_update.bundles) {
            // Fallback to config file
            for (const [bundleName, bundleConfig] of Object.entries(config.bundle_update.bundles)) {
                if (bundleConfig.include === true) {
                    excludedBundles.push(bundleName);
                }
            }
        }

        if (excludedBundles.length > 0) {
            manifestArgs.push('-ib', `'${JSON.stringify(excludedBundles)}'`);
            log.info(`📝 Excluding bundles from main package manifest: ${excludedBundles.join(', ')}`);
        }

        log.info(`node "${manifestScript}" ${manifestArgs.join(' ')}`);
        await execCommand(`node "${manifestScript}" ${manifestArgs.join(' ')}`, 'Generate main package manifest');
    }

    log.success(`🎉 Main package build completed: ${targetPath}`);
}

async function buildBundles(config, platform = 'web-mobile', environment = 'DEV', buildTemplate = 'link') {
    log.info('📦 Start bundle build process');

    const buildPath = getPlatformBuildPath(platform, buildTemplate);
    const sourcePath = path.join(buildPath, 'assets');
    const targetPath = config.bundle_update.export_directory
        ? path.resolve(grandParentDir, config.bundle_update.export_directory)
        : path.join(grandParentDir, 'build', 'bundles');

    if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true });
    }

    // 2. Copy bundle files (only copy bundles with include: true)
    if (fs.existsSync(sourcePath)) {
        const items = fs.readdirSync(sourcePath);
        const mainAssets = ['internal', 'main', 'resources'];

        for (const item of items) {
            const itemPath = path.join(sourcePath, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory() && !mainAssets.includes(item)) {
                // Only include bundles explicitly set to include: true
                let shouldInclude = false; // Default to exclude

                if (config.bundle_update && config.bundle_update.bundles && config.bundle_update.bundles[item]) {
                    const bundleConfig = config.bundle_update.bundles[item];
                    shouldInclude = bundleConfig.include === true;
                }

                if (shouldInclude) {
                    // Copy directly to target directory, no need for assets subfolder
                    const targetItemPath = path.join(targetPath, item);
                    await copyFiles(itemPath, targetItemPath, `Copy bundle: ${item}`);
                    log.info(`✅ Copied bundle: ${item} (include: true)`);
                } else {
                    log.info(`⏭️  Skipped bundle: ${item} (not include: true)`);
                }
            }
        }
    }

    // 3. Generate bundle manifest using bundle_manifest_generator.js
    const manifestScript = path.join(__dirname, '..', 'tools', 'bundle_manifest_generator.js');
    if (fs.existsSync(manifestScript)) {
        const version = config.bundle_update.version || '1.0.0';
        const serverAddress = getServerAddress(config, 'bundle_update', environment, platform);

        // Build bundleInfoMap (refer to helper.ts logic)
        const bundleInfoMap = buildBundleInfoMap(config, serverAddress, version);

        // Build ignore bundle array (exclude all not include: true)
        const ignoreBundleArray = buildIgnoreBundleArray(config, sourcePath);

        // Create temporary assets directory structure for manifest generator
        const tempAssetsPath = path.join(targetPath, 'assets');
        if (!fs.existsSync(tempAssetsPath)) {
            fs.mkdirSync(tempAssetsPath, { recursive: true });
        }

        // Move copied bundles to assets directory
        const items = fs.readdirSync(targetPath);
        for (const item of items) {
            const itemPath = path.join(targetPath, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory() && item !== 'assets') {
                const tempItemPath = path.join(tempAssetsPath, item);
                // If target already exists, remove it first
                if (fs.existsSync(tempItemPath)) {
                    fs.rmSync(tempItemPath, { recursive: true, force: true });
                }
                fs.renameSync(itemPath, tempItemPath);
            }
        }

        const manifestArgs = [
            '-v', version,
            '-u', serverAddress,
            '-s', targetPath,  // Use target directory as source, script will auto add assets
            '-d', targetPath
        ];

        // Add ignore bundle list
        if (ignoreBundleArray.length > 0) {
            manifestArgs.push('-g', ignoreBundleArray.join(','));
            log.info(`📝 Ignoring bundles: ${ignoreBundleArray.join(', ')}`);
        }

        // Add bundleInfoMap
        if (Object.keys(bundleInfoMap).length > 0) {
            manifestArgs.push('-b');
            manifestArgs.push(JSON.stringify(bundleInfoMap));
            log.info(`📝 Bundle info map: ${JSON.stringify(bundleInfoMap)}`);
        }

        // Use spawn instead of exec to avoid JSON argument parsing issues
        await execCommandWithArgs('node', [manifestScript, ...manifestArgs], 'Generate bundle manifest');

        // Move bundles back to root directory and remove assets directory
        const assetsItems = fs.readdirSync(tempAssetsPath);
        for (const item of assetsItems) {
            const tempItemPath = path.join(tempAssetsPath, item);
            const finalItemPath = path.join(targetPath, item);
            fs.renameSync(tempItemPath, finalItemPath);
        }

        // Remove empty assets directory
        fs.rmdirSync(tempAssetsPath);

        // 4. Generate bundle.json file (refer to bundle-tool pack function logic)
        const bundleJsonPath = path.join(targetPath, 'bundle.json');
        const packedBundle = {
            version: version,
            remoteManifestUrl: '',
            bundleServerAddress: serverAddress,
            bundles: {}
        };

        // Build bundles object - only include actually built bundles
        const builtBundles = fs.readdirSync(targetPath).filter(item => {
            const itemPath = path.join(targetPath, item);
            return fs.statSync(itemPath).isDirectory();
        });

        builtBundles.forEach((bundleName) => {
            if (config.bundle_update && config.bundle_update.bundles && config.bundle_update.bundles[bundleName]) {
                const bundleConfig = config.bundle_update.bundles[bundleName];
                packedBundle.bundles[bundleName] = {
                    key: bundleName,
                    md5: bundleConfig.md5 || '',
                    version: bundleConfig.version || version,
                    url: bundleConfig.url || serverAddress,
                    dependencies: bundleConfig.dependencies || []
                };
            }
        });

        // Write bundle.json file
        fs.writeFileSync(bundleJsonPath, JSON.stringify(packedBundle, null, 2));
        log.info(`📝 Generated bundle.json: ${bundleJsonPath}`);
    }

    log.success(`🎉 Bundle build completed: ${targetPath}`);
}

// Get server address
function getServerAddress(config, type, environment = 'DEV', platform = 'web-mobile') {
    // Platform name mapping
    const platformMap = {
        'web-mobile': 'WEB_MOBILE',
        'android': 'ANDROID',
        'ios': 'IOS'
    };

    const serverPlatform = platformMap[platform] || 'WEB_MOBILE';

    let addresses;
    if (type === 'main_package') {
        addresses = config.main_package.server_addresses;
    } else {
        addresses = config.bundle_update.server_addresses;
    }

    if (addresses && addresses[environment] && addresses[environment][serverPlatform]) {
        return addresses[environment][serverPlatform];
    }

    return 'https://dev.example.com/h5/';
}

// Build bundle info map (helper function)
function buildBundleInfoMap(config, serverAddress, version) {
    const bundleInfoMap = {};
    if (config.bundle_update && config.bundle_update.bundles) {
        Object.keys(config.bundle_update.bundles).forEach((key) => {
            const bundle = config.bundle_update.bundles[key];
            if (bundle.include === true) {
                bundleInfoMap[key] = {
                    url: bundle.url || serverAddress,
                    version: bundle.version || version
                };
                if (Array.isArray(bundle.dependencies) && bundle.dependencies.length > 0) {
                    bundleInfoMap[key].dependencies = bundle.dependencies;
                }
            }
        });
    }
    return bundleInfoMap;
}

// Build ignore bundle array (helper function)
function buildIgnoreBundleArray(config, sourcePath) {
    const ignoreBundleArray = [];
    if (fs.existsSync(sourcePath)) {
        const items = fs.readdirSync(sourcePath);
        const mainAssets = ['internal', 'main', 'resources'];

        for (const item of items) {
            const itemPath = path.join(sourcePath, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory() && !mainAssets.includes(item)) {
                // If not include: true, add to ignore list
                let shouldInclude = false;
                if (config.bundle_update && config.bundle_update.bundles && config.bundle_update.bundles[item]) {
                    const bundleConfig = config.bundle_update.bundles[item];
                    shouldInclude = bundleConfig.include === true;
                }

                if (!shouldInclude) {
                    ignoreBundleArray.push(item);
                }
            }
        }
    }
    return ignoreBundleArray;
}

// Show help
function showHelp() {
    console.log(`
Bundle Tool Quick Build Script

Usage:
  node scripts/quick-build.js <project-name> [options]

Options:
  project-name     Project name (e.g.: pkw-game, wpk-game)
  --platform       Target platform: web-mobile|android|ios (default: web-mobile)
  --environment    Environment: DEV|STAGE|PROD (default: DEV)
  --template       Build mode: link|default (default: link, only for native platforms)
  --main-only      Only build main package
  --bundle-only    Only build bundles
  --skip-build     Skip Cocos Creator build
  --help           Show help info

Examples:
  node scripts/quick-build.js pkw-game
  node scripts/quick-build.js pkw-game --platform android --environment PROD
  node scripts/quick-build.js pkw-game --platform ios --template default
  node scripts/quick-build.js pkw-game --main-only --platform android --template link
  node scripts/quick-build.js pkw-game --bundle-only --skip-build
`);
}

// Main function
async function main() {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.length === 0) {
        showHelp();
        process.exit(0);
    }

    const projectName = args[0];
    const platform = args.includes('--platform') ? args[args.indexOf('--platform') + 1] : 'web-mobile';
    const environment = args.includes('--environment') ? args[args.indexOf('--environment') + 1] : 'DEV';
    const buildTemplate = args.includes('--template') ? args[args.indexOf('--template') + 1] : 'link';
    const mainOnly = args.includes('--main-only');
    const bundleOnly = args.includes('--bundle-only');
    const skipBuild = args.includes('--skip-build');

    if (!projectName) {
        log.error('Please specify project name');
        showHelp();
        process.exit(1);
    }

    // Validate parameters
    const validPlatforms = ['web-mobile', 'android', 'ios'];
    const validEnvironments = ['DEV', 'STAGE', 'PROD'];
    const validTemplates = ['link', 'default'];

    if (!validPlatforms.includes(platform)) {
        log.error(`Invalid platform: ${platform}. Supported platforms: ${validPlatforms.join(', ')}`);
        process.exit(1);
    }

    if (!validEnvironments.includes(environment)) {
        log.error(`Invalid environment: ${environment}. Supported environments: ${validEnvironments.join(', ')}`);
        process.exit(1);
    }

    if (!validTemplates.includes(buildTemplate)) {
        log.error(`Invalid build mode: ${buildTemplate}. Supported modes: ${validTemplates.join(', ')}`);
        process.exit(1);
    }

    try {
        log.info(`🚀 Start building project: ${projectName}`);
        log.info(`📱 Platform: ${platform}`);
        log.info(`🌍 Environment: ${environment}`);
        if (platform === 'android' || platform === 'ios') {
            log.info(`🔧 Build mode: ${buildTemplate}`);
        }
        if (skipBuild) {
            log.info('⏭️  Skip Cocos Creator build');
        }

        const config = loadConfig(projectName);

        // 1. Execute Cocos Creator build (only once)
        await buildCocosProject(config, platform, buildTemplate, skipBuild);

        // 2. Build according to target
        if (bundleOnly) {
            await buildBundles(config, platform, environment, buildTemplate);
        } else if (mainOnly) {
            await buildMainPackage(config, platform, environment, buildTemplate);
        } else {
            await buildMainPackage(config, platform, environment, buildTemplate);
            await buildBundles(config, platform, environment, buildTemplate);
        }

        log.success('🎊 All build tasks completed!');

    } catch (error) {
        log.error(`Build failed: ${error.message}`);
        process.exit(1);
    }
}

// Start script
if (require.main === module) {
    main();
}
