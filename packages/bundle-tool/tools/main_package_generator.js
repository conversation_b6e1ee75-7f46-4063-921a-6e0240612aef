var fs = require('fs');
var path = require('path');
var crypto = require('crypto');

var dest = './remote-assets/';
var src = './jsb/';
var url = 'http://127.0.0.1:5500/';
var version = '1.0.0';
var includedBundles = null; // 包含的 bundle 列表，null 表示包含所有

// Parse arguments
var i = 2;
while (i < process.argv.length) {
    var arg = process.argv[i];

    switch (arg) {
        case '--url':
        case '-u':
            url = process.argv[i + 1];
            i += 2;
            break;
        case '--version':
        case '-v':
            version = process.argv[i + 1];
            i += 2;
            break;
        case '--src':
        case '-s':
            src = process.argv[i + 1];
            i += 2;
            break;
        case '--dest':
        case '-d':
            dest = process.argv[i + 1];
            i += 2;
            break;
        case '--included-bundles':
        case '-ib':
            try {
                includedBundles = JSON.parse(process.argv[i + 1]);
            } catch (e) {
                console.error('Invalid JSON for included bundles:', process.argv[i + 1]);
                includedBundles = null;
            }
            i += 2;
            break;
        default:
            i++;
            break;
    }
}

// Change log file path to current script directory
const LOG_PATH = path.join(__dirname, 'main_package_generator.log');
function logToFile(...args) {
    const msg = args.map(a => (typeof a === 'string' ? a : JSON.stringify(a, null, 2))).join(' ');
    fs.appendFileSync(LOG_PATH, msg + '\n');
}

// Replace all console.log and console.error with logToFile
console.log = logToFile;
console.error = logToFile;

function readDir(dir, obj) {
    var stat = null;

    try {
        stat = fs.statSync(dir);
    } catch (error) {
        stat = null;
    }

    if (!stat || !stat.isDirectory()) {
        return;
    }

    var subpaths = fs.readdirSync(dir), subpath, size, md5, compressed, relative;
    for (var i = 0; i < subpaths.length; ++i) {
        if (subpaths[i][0] === '.') {
            continue;
        }
        subpath = path.join(dir, subpaths[i]);
        stat = fs.statSync(subpath);
        if (stat.isDirectory()) {
            readDir(subpath, obj);
        }
        else if (stat.isFile()) {
            // Size in Bytes
            size = stat['size'];
            md5 = crypto.createHash('md5').update(fs.readFileSync(subpath)).digest('hex');
            compressed = path.extname(subpath).toLowerCase() === '.zip';

            relative = path.relative(src, subpath);
            console.log('relative', src, subpath, relative);

            relative = relative.replace(/\\/g, '/');
            relative = encodeURI(relative);
            obj[relative] = {
                'size': size,
                'md5': md5
            };
            if (compressed) {
                obj[relative].compressed = true;
            }
        }
    }
}

var mkdirSync = function (path) {
    try {
        fs.mkdirSync(path, { recursive: true });
    } catch (e) {
        if (e.code != 'EEXIST') throw e;
    }
}

mkdirSync(dest);

// 主包 manifest 配置
var manifest = {
    packageUrl: url,
    remoteManifestUrl: url + 'project.manifest',
    remoteVersionUrl: url + 'version.manifest',
    version: version,
    assets: {},
    searchPaths: []
};

// 读取主包资源
console.log('Reading main package assets from:', src);

// 读取 src 目录
if (fs.existsSync(path.join(src, 'src'))) {
    readDir(path.join(src, 'src'), manifest.assets);
}

// 读取 assets 目录，根据 includedBundles 条件化处理
const assetsPath = path.join(src, 'assets');
if (fs.existsSync(assetsPath)) {
    const assetItems = fs.readdirSync(assetsPath);
    for (const item of assetItems) {
        const itemPath = path.join(assetsPath, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
            // 检查是否为主包资源目录
            if (['internal', 'main', 'resources'].includes(item)) {
                console.log(`Processing main package directory: ${item}`);
                readDir(itemPath, manifest.assets);
            } else {
                // 检查是否为 bundle 目录，并根据 includedBundles 决定是否包含
                if (includedBundles === null) {
                    // 如果没有指定 includedBundles，包含所有 bundle
                    console.log(`Processing bundle directory (all included): ${item}`);
                    readDir(itemPath, manifest.assets);
                } else if (includedBundles && includedBundles.includes(item)) {
                    // 跳过包含的 bundle
                    console.log(`Skipping excluded bundle directory: ${item}`);
                } else {
                      // 如果指定了 includedBundles 且当前 bundle 不在在列表中，则需要跟随主包
                    console.log(`Processing included bundle directory: ${item}`);
                    readDir(itemPath, manifest.assets);
                }
            }
        } else if (stat.isFile()) {
            // 处理 assets 根目录下的文件
            const size = stat.size;
            const md5 = crypto.createHash('md5').update(fs.readFileSync(itemPath)).digest('hex');
            const compressed = path.extname(itemPath).toLowerCase() === '.zip';

            let relative = path.relative(src, itemPath);
            relative = relative.replace(/\\/g, '/');
            relative = encodeURI(relative);

            manifest.assets[relative] = {
                size: size,
                md5: md5
            };

            if (compressed) {
                manifest.assets[relative].compressed = true;
            }

            console.log(`Processing assets root file: ${item}`);
        }
    }
}

// 生成主包 manifest 文件
var destManifest = path.join(dest, 'project.manifest');
var destVersion = path.join(dest, 'version.manifest');

console.log(`Writing main package manifest to: ${destManifest}`);
console.log(`Main package assets count: ${Object.keys(manifest.assets).length}`);

fs.writeFile(destManifest, JSON.stringify(manifest, null, 2), (err) => {
    if (err) throw err;
    console.log('Main package manifest successfully generated');
});

// 生成版本文件（不包含 assets 信息）
var versionManifest = {
    packageUrl: manifest.packageUrl,
    remoteManifestUrl: manifest.remoteManifestUrl,
    remoteVersionUrl: manifest.remoteVersionUrl,
    version: manifest.version
};

fs.writeFile(destVersion, JSON.stringify(versionManifest, null, 2), (err) => {
    if (err) throw err;
    console.log('Main package version manifest successfully generated');
});

console.log('Main package generation completed');
