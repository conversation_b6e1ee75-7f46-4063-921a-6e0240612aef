<div class="container layout vertical flex" style="padding: 5px">

    <h1 v-if='configErrorMessage!==""' style="color: lightcoral" v-html="configErrorMessage"></h1>

    <div v-if='configErrorMessage==""' id="tab-content" class="flex-auto layout flex vertical">
        <div>
            <h2>Base Configuration</h2>
            <ui-box-container class="shadow">
                <ui-prop name="Project">
                    <div class="flex-1 layout horizontal center">
                        <ui-select @confirm="onProjectSelectChange" v-bind:value="selectedProject">
                            <option v-for="project in projectOptions" :key="project" :label="project"
                                :value="project">
                                {{project}}
                            </option>
                        </ui-select>
                    </div>
                </ui-prop>
                <ui-prop name="Environment">
                    <div class="flex-1 layout horizontal center">
                        <ui-select @confirm="onExportEnvironmentSelectChange" v-bind:value="exportEnvironment">
                            <option v-for="item in environmentOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                                {{item.label}}
                            </option>
                        </ui-select>
                    </div>
                </ui-prop>
                <ui-prop name="Platform">
                    <div class="flex-1 layout horizontal center">
                        <ui-select @confirm="onExportPlatformSelectChange" v-bind:value="exportPlatform">
                            <option v-for="item in platformOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                                {{item.label.toUpperCase()}}
                            </option>
                        </ui-select>
                    </div>
                </ui-prop>
                <ui-prop v-if="exportPlatform!=='web-mobile' && !hideiOSRelatedUI" name="Native build template">
                    <div class="flex-1 layout horizontal center">
                        <ui-select @confirm="onCocosTemplateSelectChange" v-bind:value="buildTemplateOption">
                            <option v-for="item in buildTemplateOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                                {{item.label.toUpperCase()}}
                            </option>
                        </ui-select>
                    </div>
                </ui-prop>
            </ui-box-container>
        </div>

        <!-- 更新类型单选框，单独区域，紧跟Base Configuration下方，默认选中子模块更新 -->
        <div style="margin: 16px 0 8px 0;">
            <label style="font-weight:bold; font-size: 16px; margin-right: 16px;">Update Type:</label>
            <label>
                <input type="radio" name="updateType" value="bundle" v-model="updateType" @change="onUpdateTypeChange" checked>
                Bundle Update
            </label>
            <label style="margin-right: 24px;">
                <input type="radio" name="updateType" value="main" v-model="updateType" @change="onUpdateTypeChange">
                Main Package Update
            </label>
        </div>

        <!-- Manifest Configuration 区域根据 updateType 切换显示 -->
        <div v-if="updateType==='bundle' && !hideiOSRelatedUI">
            <h2>Manifest Configuration</h2>
            <div style="padding: 0px 8px 8px">
                <div>
                    <ui-box-container class="shadow">
                        <div class="layout vertical start-justified">
                            <ui-prop name="Bundle Server Address"
                                tooltip="Input field to set the address of the bundle server.">
                                <div class="flex-1 layout horizontal center">
                                    <ui-input id="uiBundleServerAddress"
                                        v-on:blur="onInputServerAddressOver($event.target.value)"
                                        v-bind:value="manifestData.bundleServerAddress" class="flex-2">
                                    </ui-input>
                                </div>
                            </ui-prop>

                            <ui-prop name="Version"
                                tooltip="Input field to specify the version of the bundle manifest.">
                                <div class="flex-1 layout horizontal center">
                                    <ui-input id="uiVersion" @confirm="onInputVersionOver($event.target.value)"
                                        class="flex-1" v-bind:value="manifestData.version">
                                    </ui-input>
                                </div>
                            </ui-prop>
                        </div>

                        <div v-if="bundleList.length > 0" style="margin: 0px 14px 14px">
                            <h4>Bundles</h4>
                            <ui-box-container id="uiBundles">
                                <div v-for="bundle in bundleList" style="margin-bottom: 4px;">
                                    <div
                                        style="display: grid; grid-template-columns: 120px 1fr 1fr 2fr 2fr 80px; align-items: center; gap: 8px; min-width: 0;">
                                        <span
                                            style="font-weight: bold; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{bundle.key}}</span>
                                        <ui-input name="uiBundleVersion" placeholder="Version" style="min-width: 0;"
                                            v-bind:value="bundle.version"
                                            @input="onChangeBundleVersion($event.target.value, bundle.key)"
                                            @change="onChangeBundleVersion($event.target.value, bundle.key)"></ui-input>
                                        <ui-input name="uiBundleMD5" placeholder="MD5" style="min-width: 0;"
                                            readonly="true" disabled="true" v-bind:value="bundle.md5"
                                            @confirm="onChangeBundleMD5($event.target.value,bundle.key)"></ui-input>
                                        <ui-input name="uiBundleUrl" placeholder="Url" style="min-width: 0;"
                                            v-bind:value="bundle.url"
                                            @input="onChangeBundleUrl($event.target.value, bundle.key)"
                                            @change="onChangeBundleUrl($event.target.value, bundle.key)"></ui-input>
                                        <ui-input placeholder="depend: common,common-landscape" style="min-width: 0;"
                                            v-bind:value="bundle.dependencies ? bundle.dependencies.join(',') : ''"
                                            @input="onChangeBundleDependencies($event.target.value, bundle.key)"
                                            @change="onChangeBundleDependencies($event.target.value, bundle.key)"></ui-input>
                                        <ui-checkbox name="uiBundleInclude" v-bind:value="bundle.included"
                                            @confirm="onChangeBundleInclude($event.target.value, bundle)">Include</ui-checkbox>
                                    </div>
                                </div>
                            </ui-box-container>
                        </div>
                    </ui-box-container>
                </div>
            </div>
        </div>
        <div v-if="updateType==='main' && !hideiOSRelatedUI">
            <h2>Main Package Configuration</h2>
            <ui-box-container class="shadow">
                <div class="layout vertical start-justified">
                    <ui-prop name="Base Bundle Server" tooltip="主包服务器地址">
                        <div class="flex-1 layout horizontal center">
                            <ui-input id="uiMainBundleServerAddress"
                                v-on:blur="onInputMainBundleServerAddressOver($event.target.value)"
                                v-bind:value="mainBundleData.bundleServerAddress" class="flex-2"></ui-input>
                        </div>
                    </ui-prop>
                    <ui-prop name="Version" tooltip="主包版本号">
                        <div class="flex-1 layout horizontal center">
                            <ui-input id="uiMainVersion" @confirm="onInputMainVersionOver($event.target.value)"
                                class="flex-1" v-bind:value="mainBundleData.version"></ui-input>
                        </div>
                    </ui-prop>
                </div>
            </ui-box-container>
        </div>

        <div v-if="!hideiOSRelatedUI" class="end">
            <h2>Directory</h2>
            <ui-box-container class="shadow">
                <div class="layout vertical start-justified">
                    <ui-prop name="Export Directory"
                        tooltip="Input field to designate the directory for exporting bundles. Users can choose the directory by clicking the 'Select' button.">
                        <div class="flex-1 layout horizontal center">
                            <ui-input id="uiExportDir" class="flex-2"
                                v-on:blur="onInputExportPathOver($event.target.value)" v-bind:value="exportDir">
                            </ui-input>
                            <ui-button id="uiExportDirSelect" @confirm="onExportDirConfirm">Select</ui-button>
                            <ui-button @confirm="onOpenExportDir">Open</ui-button>
                        </div>
                    </ui-prop>
                </div>
            </ui-box-container>

            <h2>Operation</h2>
            <div class="layout flex horizontal" style="margin: 8px 0px">
                <ui-button class="blue" @confirm="onBuildProjectBundles">Build</ui-button>
                <ui-button class="green" @confirm="onPackProjectBundles">Pack</ui-button>
                <ui-button class="red" @confirm="onBuildAndPackProjectBundles">Build and pack</ui-button>
                <ui-button class="orange" @confirm="onPushLocalServer">Push Local Server</ui-button>
                <span class="self-center" style="margin-left: 8px" v-text="statusMessage"></span>
            </div>
        </div>
        <h1 v-if="hideiOSRelatedUI" style="color: lightcoral">
            Window NT cannot support to build iOS Platform. Please use MacOS
        </h1>

        <ui-loader v-if="isProcessing">Processing</ui-loader>
    </div>
</div>