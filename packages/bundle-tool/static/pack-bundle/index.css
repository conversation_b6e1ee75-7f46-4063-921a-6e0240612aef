.container {
    height: 100%;
    overflow-y: auto;
}

.log {
    width: 100%; height: 120px; background: #252525;
    color: #fd942b;  border-color: #fd942b;
}

.formatted-json {
    overflow: auto;
    border: 1px solid #ccc;
    padding: 10px;
    white-space: pre-wrap;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.4;
    color: #ffff;
}

/* Orange button style for Push Local Server */
ui-button.orange {
    background-color: #ff8c00 !important;
    border-color: #ff8c00 !important;
    color: white !important;
}

ui-button.orange:hover {
    background-color: #ff7700 !important;
    border-color: #ff7700 !important;
}