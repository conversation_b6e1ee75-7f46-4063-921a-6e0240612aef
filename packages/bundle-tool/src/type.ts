import chalk from 'chalk';

export type ICacheSourceData = ISourceData<ICacheBundle>;
export type IBundleSourceData = ISourceData<IPackedBundle>;
export type ICacheFileObject = FileObject<ICacheSourceData>;

export enum Project {
    PKW = 'pkw-game',
    WPK = 'wpk-game'
}

export enum Environment {
    DEV = 'DEV',
    STAGE = 'STAGE',
    PROD = 'PROD'
}

export enum DevicePlatform {
    WEB_MOBILE = 'web-mobile',
    IOS = 'ios',
    ANDROID = 'android'
}

export enum CocosNativeBuildTemplate {
    LINK = 'link',
    DEFAULT = 'default'
}

export enum BuiltPlatformDirectoryName {
    WEB_MOBILE = 'web-mobile',
    JSB_LINK = 'jsb-link',
    JSB_DEFAULT = 'jsb-default'
}

export interface IBundleToolExtension {
    load: () => void;
    unload: () => void;
    messages: {
        openPackPanel: () => void;
        replaceUuid: () => void;
    };
}

export interface IVueTemplateDOM {
    $logArea: HTMLTextAreaElement;
    shadowRoot: any;
}

export interface IVueData {
    manifestData: any;
    sourceDir: string;
    exportDir: string;
    exportProject: Project;
    exportEnvironment: Environment;
    exportPlatform: DevicePlatform;
    options: any[];
    bundleList: IVueBundle[];
    isProcessing: boolean;
    statusMessage: string;
    // build
    buildTemplateOption: string;
    buildTemplateOptions: { value: string; label: string }[];
    // error
    configErrorMessage: string;
    supportMacOS: boolean;
    hideiOSRelatedUI: boolean;
}

export type IVueBundle = ICacheBundle & { key: string; url?: string; dependencies?: string[] };

interface IBaseBundle {
    version: string;
    md5: string | undefined;
    url?: string;
    dependencies?: string[]; // 新增
}

export type ICacheBundle = IBaseBundle & {
    included: boolean;
    dependencies?: string[]; // 新增
};

export type IPackedBundle = IBaseBundle & {
    md5: string | undefined;
    dependencies?: string[]; // 新增
};

interface ISourceData<IBundleType> {
    bundleServerAddress: string;
    remoteManifestUrl: string;
    version: string;
    bundles: Record<string, IBundleType>;
}

interface FileObject<ISourceData> {
    platformName: DevicePlatform;
    buildTemplateOption: CocosNativeBuildTemplate;
    sourcePath: string;
    sourceData: ISourceData;
    exportPath: string;
}

export interface IConsole {
    log(...args: any): any;
    info(...args: any): any;
    warn(...args: any): any;
    error(...args: any): any;
}

export class TerminalConsole implements IConsole {
    error(...args: any): any {
        console.log(chalk.red(...args));
    }

    info(...args: any): any {
        console.log(chalk.blue(...args));
    }

    log(...args: any): any {
        console.log(chalk.green(...args));
    }

    warn(...args: any): any {
        console.log(chalk.yellow(...args));
    }
}

export class EditorConsole implements IConsole {
    log(args: any): any {
        Editor.info(args);
    }

    info(args: any): any {
        Editor.info(args);
    }

    warn(args: any): any {
        Editor.warn(args);
    }

    error(args: any): any {
        Editor.error(args);
    }
}

export interface IEnv {
    console: IConsole;
    context: any;
}

export type PartialRecord<K extends keyof any, T> = {
    [P in K]?: T;
};

// 更新类型枚举
export enum UpdateType {
    BUNDLE = 'bundle',
    MAIN = 'main'
}

// 主包配置接口
export interface IMainPackageConfig {
    version: string;
    bundleServerAddress: string;
}

// 主配置文件结构接口（项目列表）
export interface IMainConfig {
    projects: string[];
}

// 项目配置文件结构接口
export interface IProjectConfig {
    mainPackage: {
        version: string;
        serverAddresses: Record<string, Record<string, string>>;
        exportDirectory: string;
    };
    bundleUpdate: {
        exportDirectory: string;
        serverAddresses: Record<string, Record<string, string>>;
        bundles: Record<string, {
            version: string;
            md5: string;
            url: string;
            include: boolean;
            dependencies?: string[];
        }>;
    };
}

// 向后兼容的YAML配置接口（已废弃，保留用于迁移）
export interface IYamlConfig {
    mainPackage: {
        version: string;
        serverAddresses: Record<string, Record<string, string>>;
        exportDirectory: string;
    };
    bundleUpdate: {
        exportDirectory: string;
        serverAddresses: Record<string, Record<string, string>>;
        bundles: Record<string, {
            version: string;
            md5: string;
            url: string;
            dependencies?: string[];
        }>;
    };
}

// 扩展 IVueData 接口以支持主包配置、更新类型和项目选择
export interface IVueDataExtended extends IVueData {
    updateType: UpdateType;
    mainBundleData: IMainPackageConfig;
    selectedProject: string;
    projectOptions: string[];
}
