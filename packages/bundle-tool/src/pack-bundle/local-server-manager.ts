import * as fs from 'fs';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { EditorConsole } from '../type';

export interface IServerConfig {
    mainPackage: {
        port: number;
        path: string;
    };
    bundles: Array<{
        name: string;
        port: number;
        path: string;
    }>;
}

export interface IBundleJson {
    version: string;
    remoteManifestUrl: string;
    bundleServerAddress: string;
    bundles: Record<string, {
        key: string;
        md5: string;
        version: string;
        url: string;
        dependencies: string[];
    }>;
}

export class LocalServerManager {
    private console: EditorConsole;
    private runningProcesses: Map<number, ChildProcess> = new Map();
    private serverConfigPath: string;

    constructor(console: EditorConsole, projectPath: string) {
        this.console = console;
        this.serverConfigPath = path.join(projectPath, 'packages', 'bundle-tool', 'httpserver');
    }

    /**
     * 从 bundle.json 解析服务器配置
     */
    parseServerConfig(bundleJsonPath: string, mainPackagePath: string): IServerConfig {
        const config: IServerConfig = {
            mainPackage: {
                port: 3000,
                path: mainPackagePath
            },
            bundles: []
        };

        try {
            if (fs.existsSync(bundleJsonPath)) {
                const bundleData: IBundleJson = JSON.parse(fs.readFileSync(bundleJsonPath, 'utf-8'));
                
                // 解析主包端口（从 bundleServerAddress 提取）
                if (bundleData.bundleServerAddress) {
                    try {
                        const mainUrl = new URL(bundleData.bundleServerAddress);
                        const mainPort = parseInt(mainUrl.port, 10) || 3000;
                        config.mainPackage.port = mainPort;
                    } catch (error) {
                        this.console.warn(`Invalid main package URL: ${bundleData.bundleServerAddress}`);
                    }
                }

                // 解析每个 bundle 的端口
                if (bundleData.bundles) {
                    for (const [bundleName, bundleInfo] of Object.entries(bundleData.bundles)) {
                        if (bundleInfo.url) {
                            try {
                                const bundleUrl = new URL(bundleInfo.url);
                                const bundlePort = parseInt(bundleUrl.port, 10) || 3001;
                                const bundlePath = path.dirname(bundleJsonPath); // bundles 目录
                                
                                config.bundles.push({
                                    name: bundleName,
                                    port: bundlePort,
                                    path: bundlePath
                                });
                            } catch (error) {
                                this.console.warn(`Invalid URL for bundle ${bundleName}: ${bundleInfo.url}`);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            this.console.error(`Error parsing bundle.json: ${error}`);
        }

        return config;
    }

    /**
     * 复制文件到服务器目录
     */
    async copyToServerDirectory(sourcePath: string, targetPath: string): Promise<void> {
        return new Promise((resolve, reject) => {
            // 确保目标目录存在
            const targetDir = path.dirname(targetPath);
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
            }

            // 使用 cp 命令复制文件
            const copyProcess = spawn('cp', ['-r', sourcePath, targetPath], {
                stdio: 'pipe'
            });

            copyProcess.on('close', (code) => {
                if (code === 0) {
                    this.console.info(`Copied: ${sourcePath} -> ${targetPath}`);
                    resolve();
                } else {
                    const error = new Error(`Copy failed with code ${code}`);
                    this.console.error(`Copy failed: ${sourcePath} -> ${targetPath} - ${error.message}`);
                    reject(error);
                }
            });

            copyProcess.on('error', (error) => {
                this.console.error(`Copy error: ${sourcePath} -> ${targetPath} - ${error.message}`);
                reject(error);
            });
        });
    }

    /**
     * 杀死指定端口的进程
     */
    async killPortProcess(port: number): Promise<void> {
        return new Promise((resolve) => {
            const killCommand = process.platform === 'win32' 
                ? `netstat -ano | findstr :${port}` 
                : `lsof -ti:${port}`;
            
            const killProcess = spawn('sh', ['-c', killCommand], { stdio: 'pipe' });
            
            killProcess.stdout.on('data', (data) => {
                const output = data.toString().trim();
                if (output) {
                    const finalKillCommand = process.platform === 'win32'
                        ? `taskkill /PID ${output.split(/\s+/).pop()} /F`
                        : `kill -9 ${output}`;
                    
                    spawn('sh', ['-c', finalKillCommand], { stdio: 'inherit' });
                    this.console.info(`Killed process on port ${port}`);
                }
            });
            
            killProcess.on('close', () => {
                resolve();
            });
        });
    }

    /**
     * 启动本地服务器
     */
    async startLocalServers(bundleJsonPath: string, mainPackagePath: string, bundlesPath: string): Promise<void> {
        try {
            this.console.info('🚀 Starting local servers with auto port assignment...');

            // 启动服务器进程
            const serverScript = path.join(this.serverConfigPath, 'server.js');
            const serverProcess = spawn('node', [
                serverScript,
                '--bundle-json', bundleJsonPath,
                '--main-package', mainPackagePath,
                '--bundles', bundlesPath
            ], {
                stdio: 'pipe',
                cwd: this.serverConfigPath
            });

            // 处理服务器输出
            serverProcess.stdout?.on('data', (data) => {
                const output = data.toString();
                this.console.info(`[Server] ${output.trim()}`);
            });

            serverProcess.stderr?.on('data', (data) => {
                const output = data.toString();
                this.console.warn(`[Server Error] ${output.trim()}`);
            });

            serverProcess.on('error', (error) => {
                this.console.error(`Failed to start local server: ${error.message}`);
            });

            serverProcess.on('close', (code) => {
                if (code === 0) {
                    this.console.info('Local server process completed successfully');
                } else {
                    this.console.error(`Local server process exited with code ${code}`);
                }
            });

            // 记录进程以便后续管理
            const processId = Date.now();
            this.runningProcesses.set(processId, serverProcess);

            // 等待一段时间让服务器启动
            await new Promise(resolve => setTimeout(resolve, 2000));

            this.console.info('🎉 Local servers started successfully!');
            this.console.info('📋 Server URLs:');
            this.console.info('   • Check console output above for specific URLs');
            this.console.info('   • Main Package: http://192.168.x.x:30001/');
            this.console.info('   • Bundles: http://192.168.x.x:30002+/');

        } catch (error) {
            this.console.error(`Failed to start local servers: ${(error as Error).message}`);
            throw error;
        }
    }

    /**
     * 停止所有运行中的服务器
     */
    stopAllServers(): void {
        for (const [id, process] of this.runningProcesses) {
            try {
                process.kill('SIGTERM');
                this.runningProcesses.delete(id);
                this.console.info(`Stopped server process ${id}`);
            } catch (error) {
                this.console.warn(`Failed to stop server process ${id}: ${(error as Error).message}`);
            }
        }
    }
}
