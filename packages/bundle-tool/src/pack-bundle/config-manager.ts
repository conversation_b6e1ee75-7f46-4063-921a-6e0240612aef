import * as path from 'path';
import * as fs from 'fs';
import type { IYamlConfig, IMainConfig, IProjectConfig } from '../type';
import { UpdateType } from '../type';

// 临时使用简单的 YAML 解析器，避免依赖问题
function parseSimpleYaml(yamlContent: string): any {
    const lines = yamlContent.split('\n');
    const result: any = {};
    const stack: any[] = [result];
    let currentIndent = 0;

    for (let line of lines) {
        // 跳过注释和空行
        if (line.trim().startsWith('#') || line.trim() === '') {
            continue;
        }

        const indent = line.length - line.trimStart().length;
        const trimmedLine = line.trim();

        if (trimmedLine.includes(':')) {
            const [key, ...valueParts] = trimmedLine.split(':');
            const value = valueParts.join(':').trim();

            // 处理缩进层级
            while (stack.length > 1 && indent <= currentIndent) {
                stack.pop();
                currentIndent -= 2;
            }

            const current = stack[stack.length - 1];

            if (value === '' || value === '{}') {
                // 这是一个对象
                current[key.trim()] = {};
                stack.push(current[key.trim()]);
                currentIndent = indent;
            } else if (value.startsWith('"') && value.endsWith('"')) {
                // 字符串值
                current[key.trim()] = value.slice(1, -1);
            } else {
                // 其他值
                current[key.trim()] = value;
            }
        } else if (trimmedLine.startsWith('- ')) {
            // 数组项
            const current = stack[stack.length - 1];
            const arrayKey = Object.keys(current).pop();
            if (arrayKey && !Array.isArray(current[arrayKey])) {
                current[arrayKey] = [];
            }
            if (arrayKey) {
                const value = trimmedLine.slice(2).trim();
                if (value.startsWith('"') && value.endsWith('"')) {
                    current[arrayKey].push(value.slice(1, -1));
                } else {
                    current[arrayKey].push(value);
                }
            }
        }
    }

    return result;
}

// 将主配置 YAML 转换为 TypeScript 接口
function convertMainConfigYamlToTypescript(yamlConfig: any): IMainConfig {
    return {
        projects: yamlConfig.projects || []
    };
}

// 将项目配置 YAML 转换为 TypeScript 接口
function convertProjectConfigYamlToTypescript(yamlConfig: any): IProjectConfig {
    const converted: IProjectConfig = {
        mainPackage: {
            version: yamlConfig.main_package?.version || '1.0.0',
            serverAddresses: yamlConfig.main_package?.server_addresses || {},
            exportDirectory: yamlConfig.main_package?.export_directory || './build/main-package'
        },
        bundleUpdate: {
            exportDirectory: yamlConfig.bundle_update?.export_directory || './build/bundles',
            serverAddresses: yamlConfig.bundle_update?.server_addresses || {},
            bundles: yamlConfig.bundle_update?.bundles || {}
        }
    };
    return converted;
}

// 将 YAML 中的 snake_case 属性转换为 TypeScript 中的 camelCase（向后兼容）
function convertYamlToTypescript(yamlConfig: any): IYamlConfig {
    const converted: IYamlConfig = {
        mainPackage: {
            version: yamlConfig.main_package?.version || '1.0.0',
            serverAddresses: yamlConfig.main_package?.server_addresses || {},
            exportDirectory: yamlConfig.main_package?.export_directory || './build/main-package'
        },
        bundleUpdate: {
            exportDirectory: yamlConfig.bundle_update?.export_directory || './build/bundles',
            serverAddresses: yamlConfig.bundle_update?.server_addresses || {},
            bundles: yamlConfig.bundle_update?.bundles || {}
        }
    };
    return converted;
}

/**
 * 分层化配置管理器
 * 负责读取和管理主配置文件和项目配置文件
 */
export default class ConfigManager {
    private static mainConfigPath = path.resolve(__dirname, '../../config/config.yaml');
    private static mainConfig: IMainConfig | null = null;
    private static currentProject: string | null = null;
    private static projectConfig: IProjectConfig | null = null;

    // 向后兼容的旧配置
    private static legacyConfig: IYamlConfig | null = null;

    /**
     * 加载主配置文件（项目列表）
     */
    private static loadMainConfig(): void {
        if (!this.mainConfig) {
            try {
                if (fs.existsSync(this.mainConfigPath)) {
                    const yamlContent = fs.readFileSync(this.mainConfigPath, 'utf-8');
                    const yamlConfig = parseSimpleYaml(yamlContent);
                    this.mainConfig = convertMainConfigYamlToTypescript(yamlConfig);
                } else {
                    // 如果主配置文件不存在，尝试从旧配置迁移
                    this.migrateFromLegacyConfig();
                }
            } catch (error) {
                Editor.error('[ConfigManager] Failed to load main config.yaml:', error);
                // 回退到默认主配置
                this.mainConfig = this.getDefaultMainConfig();
            }
        }
    }

    /**
     * 加载项目配置文件
     */
    private static loadProjectConfig(projectName: string): void {
        if (this.currentProject !== projectName || !this.projectConfig) {
            try {
                const projectConfigPath = path.resolve(__dirname, `../../config/${projectName}.yaml`);
                if (fs.existsSync(projectConfigPath)) {
                    const yamlContent = fs.readFileSync(projectConfigPath, 'utf-8');
                    const yamlConfig = parseSimpleYaml(yamlContent);
                    this.projectConfig = convertProjectConfigYamlToTypescript(yamlConfig);
                    this.currentProject = projectName;
                } else {
                    throw new Error(`Project config file not found: ${projectConfigPath}`);
                }
            } catch (error) {
                Editor.error(`[ConfigManager] Failed to load project config for ${projectName}:`, error);
                // 回退到默认项目配置
                this.projectConfig = this.getDefaultProjectConfig();
                this.currentProject = projectName;
            }
        }
    }

    /**
     * 从旧配置迁移到新的分层配置
     */
    private static migrateFromLegacyConfig(): void {
        // 尝试从旧的统一配置文件迁移
        const legacyConfigPath = path.resolve(__dirname, '../../config/config.yaml.backup');
        if (fs.existsSync(legacyConfigPath)) {
            try {
                const yamlContent = fs.readFileSync(legacyConfigPath, 'utf-8');
                const yamlConfig = parseSimpleYaml(yamlContent);
                this.legacyConfig = convertYamlToTypescript(yamlConfig);

                // 创建默认的主配置
                this.mainConfig = this.getDefaultMainConfig();
                Editor.info('[ConfigManager] Migrated from legacy config format');
            } catch (error) {
                Editor.error('[ConfigManager] Failed to migrate legacy config:', error);
                this.mainConfig = this.getDefaultMainConfig();
            }
        } else {
            // 尝试从更旧的 JSON 配置迁移
            this.migrateFromOldJsonConfig();
        }
    }

    /**
     * 从旧的 bundle-server-address.json 迁移配置
     */
    private static migrateFromOldJsonConfig(): void {
        const oldConfigPath = path.resolve(__dirname, '../../config/bundle-server-address.json');
        if (fs.existsSync(oldConfigPath)) {
            try {
                const oldConfig = JSON.parse(fs.readFileSync(oldConfigPath, 'utf-8'));
                this.legacyConfig = this.convertOldConfigToNew(oldConfig);
                this.mainConfig = this.getDefaultMainConfig();
                Editor.info('[ConfigManager] Migrated from old JSON config format');
            } catch (error) {
                Editor.error('[ConfigManager] Failed to migrate old JSON config:', error);
                this.mainConfig = this.getDefaultMainConfig();
            }
        } else {
            this.mainConfig = this.getDefaultMainConfig();
        }
    }

    /**
     * 将旧配置格式转换为新格式
     */
    private static convertOldConfigToNew(oldConfig: any): IYamlConfig {
        const newConfig: IYamlConfig = {
            mainPackage: {
                version: '1.0.0',
                serverAddresses: {},
                exportDirectory: './build/main-package'
            },
            bundleUpdate: {
                exportDirectory: './build/bundles',
                serverAddresses: {},
                bundles: {}
            }
        };

        // 转换服务器地址
        for (const env in oldConfig) {
            if (oldConfig[env] && typeof oldConfig[env] === 'object') {
                newConfig.mainPackage.serverAddresses[env] = {};
                newConfig.bundleUpdate.serverAddresses[env] = {};

                for (const platform in oldConfig[env]) {
                    if (platform !== 'BUNDLES') {
                        newConfig.mainPackage.serverAddresses[env][platform] = oldConfig[env][platform];
                        newConfig.bundleUpdate.serverAddresses[env][platform] = oldConfig[env][platform];
                    }
                }

                // 转换 Bundle 配置
                if (oldConfig[env].BUNDLES) {
                    for (const bundleName in oldConfig[env].BUNDLES) {
                        newConfig.bundleUpdate.bundles[bundleName] = oldConfig[env].BUNDLES[bundleName];
                    }
                }
            }
        }

        return newConfig;
    }

    /**
     * 获取默认主配置
     */
    private static getDefaultMainConfig(): IMainConfig {
        return {
            projects: ['pkw-game', 'wpk-game']
        };
    }

    /**
     * 获取默认项目配置
     */
    private static getDefaultProjectConfig(): IProjectConfig {
        return {
            mainPackage: {
                version: '1.0.0',
                serverAddresses: {
                    DEV: {
                        WEB_MOBILE: 'https://dev.example.com/game/h5/',
                        IOS: 'https://dev.example.com/game/native/',
                        ANDROID: 'https://dev.example.com/game/native/'
                    }
                },
                exportDirectory: './build/main-package'
            },
            bundleUpdate: {
                exportDirectory: './build/bundles',
                serverAddresses: {
                    DEV: {
                        WEB_MOBILE: 'https://dev.example.com/game/h5/',
                        IOS: 'https://dev.example.com/game/native/',
                        ANDROID: 'https://dev.example.com/game/native/'
                    }
                },
                bundles: {}
            }
        };
    }

    /**
     * 获取默认配置（向后兼容）
     */
    private static getDefaultConfig(): IYamlConfig {
        return {
            mainPackage: {
                version: '1.0.0',
                serverAddresses: {
                    DEV: {
                        WEB_MOBILE: 'https://dev.example.com/game/h5/',
                        IOS: 'https://dev.example.com/game/native/',
                        ANDROID: 'https://dev.example.com/game/native/'
                    }
                },
                exportDirectory: './build/main-package'
            },
            bundleUpdate: {
                exportDirectory: './build/bundles',
                serverAddresses: {
                    DEV: {
                        WEB_MOBILE: 'https://dev.example.com/game/h5/',
                        IOS: 'https://dev.example.com/game/native/',
                        ANDROID: 'https://dev.example.com/game/native/'
                    }
                },
                bundles: {}
            }
        };
    }

    /**
     * 获取项目列表
     */
    static getProjectList(): string[] {
        this.loadMainConfig();
        if (!this.mainConfig) {
            throw new Error('Main config not loaded');
        }
        return this.mainConfig.projects;
    }

    /**
     * 设置当前项目
     */
    static setCurrentProject(projectName: string): void {
        this.loadProjectConfig(projectName);
    }

    /**
     * 获取当前项目名称
     */
    static getCurrentProject(): string | null {
        return this.currentProject;
    }

    /**
     * 获取服务器地址
     */
    static getServerAddress(updateType: UpdateType, env: string, platform: string, projectName?: string): string {
        if (projectName) {
            this.loadProjectConfig(projectName);
        }

        if (!this.projectConfig) {
            throw new Error('Project config not loaded');
        }

        env = env.toUpperCase();
        platform = platform.toUpperCase().replace('-', '_');

        const serverAddresses = updateType === UpdateType.MAIN
            ? this.projectConfig.mainPackage.serverAddresses
            : this.projectConfig.bundleUpdate.serverAddresses;

        if (!serverAddresses[env] || !serverAddresses[env][platform]) {
            throw new Error(`No server address found for ${updateType} update, env: ${env}, platform: ${platform}`);
        }

        return serverAddresses[env][platform];
    }

    /**
     * 获取导出目录
     */
    static getExportDirectory(updateType: UpdateType, projectName?: string): string {
        if (projectName) {
            this.loadProjectConfig(projectName);
        }

        if (!this.projectConfig) {
            throw new Error('Project config not loaded');
        }

        return updateType === UpdateType.MAIN
            ? this.projectConfig.mainPackage.exportDirectory
            : this.projectConfig.bundleUpdate.exportDirectory;
    }

    /**
     * 获取主包版本
     */
    static getMainPackageVersion(projectName?: string): string {
        if (projectName) {
            this.loadProjectConfig(projectName);
        }

        // 如果有项目配置，返回项目配置中的版本
        if (this.projectConfig) {
            return this.projectConfig.mainPackage.version;
        }

        // 如果没有项目配置，尝试使用旧配置
        if (this.legacyConfig && this.legacyConfig.mainPackage && this.legacyConfig.mainPackage.version) {
            return this.legacyConfig.mainPackage.version;
        }

        // 如果都没有，返回默认版本
        Editor.warn('[bundle-tool] No project config loaded, returning default version');
        return '1.0.0';
    }

    /**
     * 获取 Bundle 配置
     */
    static getBundleConfig(bundleName: string, projectName?: string): any {
        if (projectName) {
            this.loadProjectConfig(projectName);
        }

        // 如果有项目配置，返回项目配置中的 bundle
        if (this.projectConfig) {
            return this.projectConfig.bundleUpdate.bundles[bundleName] || null;
        }

        // 如果没有项目配置，尝试使用旧配置
        if (this.legacyConfig && this.legacyConfig.bundleUpdate && this.legacyConfig.bundleUpdate.bundles) {
            return this.legacyConfig.bundleUpdate.bundles[bundleName] || null;
        }

        // 如果都没有，返回 null
        return null;
    }

    /**
     * 获取所有 Bundle 配置
     */
    static getAllBundleConfigs(projectName?: string): Record<string, any> {
        if (projectName) {
            this.loadProjectConfig(projectName);
        }

        // 如果有项目配置，返回项目配置中的 bundles
        if (this.projectConfig) {
            return this.projectConfig.bundleUpdate.bundles;
        }

        // 如果没有项目配置，尝试使用旧配置
        if (this.legacyConfig && this.legacyConfig.bundleUpdate && this.legacyConfig.bundleUpdate.bundles) {
            return this.legacyConfig.bundleUpdate.bundles;
        }

        // 如果都没有，返回空对象而不是抛出错误
        Editor.warn('[bundle-tool] No project config loaded, returning empty bundle configs');
        return {};
    }

    /**
     * 获取完整项目配置
     */
    static getFullProjectConfig(projectName?: string): IProjectConfig {
        if (projectName) {
            this.loadProjectConfig(projectName);
        }

        if (!this.projectConfig) {
            throw new Error('Project config not loaded');
        }

        return this.projectConfig;
    }

    /**
     * 获取完整配置（向后兼容）
     */
    static getFullConfig(): IYamlConfig {
        // 如果有项目配置，返回项目配置
        if (this.projectConfig) {
            return {
                mainPackage: this.projectConfig.mainPackage,
                bundleUpdate: this.projectConfig.bundleUpdate
            };
        }

        // 否则返回旧的配置
        if (this.legacyConfig) {
            return this.legacyConfig;
        }

        // 最后返回默认配置
        return this.getDefaultConfig();
    }
}
