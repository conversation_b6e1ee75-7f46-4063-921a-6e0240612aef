import * as path from 'path';
import * as fs from 'fs';
import type { IYamlConfig} from '../type';
import { UpdateType } from '../type';

// 临时使用简单的 YAML 解析器，避免依赖问题
function parseSimpleYaml(yamlContent: string): any {
    const lines = yamlContent.split('\n');
    const result: any = {};
    const stack: any[] = [result];
    let currentIndent = 0;

    for (let line of lines) {
        // 跳过注释和空行
        if (line.trim().startsWith('#') || line.trim() === '') {
            continue;
        }

        const indent = line.length - line.trimStart().length;
        const trimmedLine = line.trim();

        if (trimmedLine.includes(':')) {
            const [key, ...valueParts] = trimmedLine.split(':');
            const value = valueParts.join(':').trim();

            // 处理缩进层级
            while (stack.length > 1 && indent <= currentIndent) {
                stack.pop();
                currentIndent -= 2;
            }

            const current = stack[stack.length - 1];

            if (value === '' || value === '{}') {
                // 这是一个对象
                current[key.trim()] = {};
                stack.push(current[key.trim()]);
                currentIndent = indent;
            } else if (value.startsWith('"') && value.endsWith('"')) {
                // 字符串值
                current[key.trim()] = value.slice(1, -1);
            } else {
                // 其他值
                current[key.trim()] = value;
            }
        } else if (trimmedLine.startsWith('- ')) {
            // 数组项
            const current = stack[stack.length - 1];
            const arrayKey = Object.keys(current).pop();
            if (arrayKey && !Array.isArray(current[arrayKey])) {
                current[arrayKey] = [];
            }
            if (arrayKey) {
                const value = trimmedLine.slice(2).trim();
                if (value.startsWith('"') && value.endsWith('"')) {
                    current[arrayKey].push(value.slice(1, -1));
                } else {
                    current[arrayKey].push(value);
                }
            }
        }
    }

    return result;
}

// 将 YAML 中的 snake_case 属性转换为 TypeScript 中的 camelCase
function convertYamlToTypescript(yamlConfig: any): IYamlConfig {
    const converted: IYamlConfig = {
        mainPackage: {
            version: yamlConfig.main_package?.version || '1.0.0',
            serverAddresses: yamlConfig.main_package?.server_addresses || {},
            exportDirectory: yamlConfig.main_package?.export_directory || './build/main-package'
        },
        bundleUpdate: {
            exportDirectory: yamlConfig.bundle_update?.export_directory || './build/bundles',
            serverAddresses: yamlConfig.bundle_update?.server_addresses || {},
            bundles: yamlConfig.bundle_update?.bundles || {}
        }
    };
    return converted;
}

/**
 * 统一配置管理器
 * 负责读取和管理 config.yaml 文件
 */
export default class ConfigManager {
    private static configPath = path.resolve(__dirname, '../../config/config.yaml');
    private static config: IYamlConfig | null = null;

    /**
     * 加载配置文件
     */
    private static loadConfig(): void {
        if (!this.config) {
            try {
                if (fs.existsSync(this.configPath)) {
                    const yamlContent = fs.readFileSync(this.configPath, 'utf-8');
                    const yamlConfig = parseSimpleYaml(yamlContent);
                    this.config = convertYamlToTypescript(yamlConfig);
                } else {
                    // 如果 YAML 文件不存在，尝试从旧的 JSON 文件迁移
                    this.migrateFromOldConfig();
                }
            } catch (error) {
                Editor.error('[ConfigManager] Failed to load config.yaml:', error);
                // 回退到默认配置
                this.config = this.getDefaultConfig();
            }
        }
    }

    /**
     * 从旧的 bundle-server-address.json 迁移配置
     */
    private static migrateFromOldConfig(): void {
        const oldConfigPath = path.resolve(__dirname, '../../config/bundle-server-address.json');
        if (fs.existsSync(oldConfigPath)) {
            try {
                const oldConfig = JSON.parse(fs.readFileSync(oldConfigPath, 'utf-8'));
                this.config = this.convertOldConfigToNew(oldConfig);
                Editor.info('[ConfigManager] Migrated from old config format');
            } catch (error) {
                Editor.error('[ConfigManager] Failed to migrate old config:', error);
                this.config = this.getDefaultConfig();
            }
        } else {
            this.config = this.getDefaultConfig();
        }
    }

    /**
     * 将旧配置格式转换为新格式
     */
    private static convertOldConfigToNew(oldConfig: any): IYamlConfig {
        const newConfig: IYamlConfig = {
            mainPackage: {
                version: '1.0.0',
                serverAddresses: {},
                exportDirectory: './build/main-package'
            },
            bundleUpdate: {
                exportDirectory: './build/bundles',
                serverAddresses: {},
                bundles: {}
            }
        };

        // 转换服务器地址
        for (const env in oldConfig) {
            if (oldConfig[env] && typeof oldConfig[env] === 'object') {
                newConfig.mainPackage.serverAddresses[env] = {};
                newConfig.bundleUpdate.serverAddresses[env] = {};

                for (const platform in oldConfig[env]) {
                    if (platform !== 'BUNDLES') {
                        newConfig.mainPackage.serverAddresses[env][platform] = oldConfig[env][platform];
                        newConfig.bundleUpdate.serverAddresses[env][platform] = oldConfig[env][platform];
                    }
                }

                // 转换 Bundle 配置
                if (oldConfig[env].BUNDLES) {
                    for (const bundleName in oldConfig[env].BUNDLES) {
                        newConfig.bundleUpdate.bundles[bundleName] = oldConfig[env].BUNDLES[bundleName];
                    }
                }
            }
        }

        return newConfig;
    }

    /**
     * 获取默认配置
     */
    private static getDefaultConfig(): IYamlConfig {
        return {
            mainPackage: {
                version: '1.0.0',
                serverAddresses: {
                    DEV: {
                        WEB_MOBILE: 'https://dev.example.com/game/h5/',
                        IOS: 'https://dev.example.com/game/native/',
                        ANDROID: 'https://dev.example.com/game/native/'
                    }
                },
                exportDirectory: './build/main-package'
            },
            bundleUpdate: {
                exportDirectory: './build/bundles',
                serverAddresses: {
                    DEV: {
                        WEB_MOBILE: 'https://dev.example.com/game/h5/',
                        IOS: 'https://dev.example.com/game/native/',
                        ANDROID: 'https://dev.example.com/game/native/'
                    }
                },
                bundles: {}
            }
        };
    }

    /**
     * 获取服务器地址
     */
    static getServerAddress(updateType: UpdateType, env: string, platform: string): string {
        this.loadConfig();
        if (!this.config) {
            throw new Error('Config not loaded');
        }

        env = env.toUpperCase();
        platform = platform.toUpperCase().replace('-', '_');

        const serverAddresses = updateType === UpdateType.MAIN
            ? this.config.mainPackage.serverAddresses
            : this.config.bundleUpdate.serverAddresses;

        if (!serverAddresses[env] || !serverAddresses[env][platform]) {
            throw new Error(`No server address found for ${updateType} update, env: ${env}, platform: ${platform}`);
        }

        return serverAddresses[env][platform];
    }

    /**
     * 获取导出目录
     */
    static getExportDirectory(updateType: UpdateType): string {
        this.loadConfig();
        if (!this.config) {
            throw new Error('Config not loaded');
        }

        return updateType === UpdateType.MAIN
            ? this.config.mainPackage.exportDirectory
            : this.config.bundleUpdate.exportDirectory;
    }

    /**
     * 获取主包版本
     */
    static getMainPackageVersion(): string {
        this.loadConfig();
        if (!this.config) {
            throw new Error('Config not loaded');
        }

        return this.config.mainPackage.version;
    }

    /**
     * 获取 Bundle 配置
     */
    static getBundleConfig(bundleName: string): any {
        this.loadConfig();
        if (!this.config) {
            throw new Error('Config not loaded');
        }

        return this.config.bundleUpdate.bundles[bundleName] || null;
    }

    /**
     * 获取所有 Bundle 配置
     */
    static getAllBundleConfigs(): Record<string, any> {
        this.loadConfig();
        if (!this.config) {
            throw new Error('Config not loaded');
        }

        return this.config.bundleUpdate.bundles;
    }

    /**
     * 获取完整配置
     */
    static getFullConfig(): IYamlConfig {
        this.loadConfig();
        if (!this.config) {
            throw new Error('Config not loaded');
        }

        return this.config;
    }
}
