const http = require('http');
const fs = require('fs').promises;
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');

// --- 配置区 ---
const DEFAULT_PORT = 3000;
const BASE_PUBLIC_DIRECTORY = path.join(__dirname, 'public');

const mimeTypes = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm',
    '.ico': 'image/x-icon',
    '.atlas': 'text/plain' // 添加 .atlas 文件的 MIME 类型
};

// 存储运行中的服务器实例
const runningServers = new Map();

// 创建单个服务器实例
function createServer(port, publicDirectory, serverName = 'Server') {
    const server = http.createServer(async (req, res) => {
        try {
            // 解析请求的 URL，去除查询字符串
            const parsedUrl = url.parse(req.url);
            let requestedPath = parsedUrl.pathname === '/' ? '/index.html' : parsedUrl.pathname;

            // 解码 URL 路径，处理可能的编码字符
            requestedPath = decodeURIComponent(requestedPath);

            // 构建文件的绝对路径
            const filePath = path.join(publicDirectory, requestedPath);

            // --- 安全检查：防止目录遍历攻击 ---
            const normalizedPath = path.normalize(filePath);
            if (!normalizedPath.startsWith(publicDirectory)) {
                console.warn(`[SECURITY] Forbidden access attempt: ${req.url}`);
                res.writeHead(403, { 'Content-Type': 'text/plain' });
                res.end('403 Forbidden');
                return;
            }

            // 异步读取文件内容
            const fileContent = await fs.readFile(filePath);

            // 获取文件扩展名以确定 MIME 类型
            const extname = path.extname(filePath).toLowerCase();
            const contentType = mimeTypes[extname] || 'application/octet-stream';

            // 发送成功的响应
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(fileContent);

        } catch (error) {
            // --- 错误处理 ---
            if (error.code === 'ENOENT') {
                console.error(`[404] ${serverName} - File not found: ${req.url}`);
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>');
            } else {
                console.error(`[500] ${serverName} - Server error for ${req.url}:`, error);
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end('<h1>500 Internal Server Error</h1>');
            }
        }
    });

    return server;
}

// 杀死指定端口的进程
async function killPortProcess(port) {
    return new Promise((resolve) => {
        const killCommand = process.platform === 'win32'
            ? `netstat -ano | findstr :${port}`
            : `lsof -ti:${port}`;

        const killProcess = spawn('sh', ['-c', killCommand], { stdio: 'pipe' });

        killProcess.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                const finalKillCommand = process.platform === 'win32'
                    ? `taskkill /PID ${output.split(/\s+/).pop()} /F`
                    : `kill -9 ${output}`;

                spawn('sh', ['-c', finalKillCommand], { stdio: 'inherit' });
                console.log(`Killed process on port ${port}`);
            }
        });

        killProcess.on('close', () => {
            resolve();
        });
    });
}

// 启动服务器
async function startServer(port, publicDirectory, serverName = 'Server') {
    // 先杀死可能占用端口的进程
    await killPortProcess(port);

    // 如果已有服务器在运行，先关闭
    if (runningServers.has(port)) {
        const existingServer = runningServers.get(port);
        existingServer.close();
        runningServers.delete(port);
    }

    const server = createServer(port, publicDirectory, serverName);

    return new Promise((resolve, reject) => {
        server.listen(port, (error) => {
            if (error) {
                console.error(`Failed to start ${serverName} on port ${port}:`, error);
                reject(error);
            } else {
                console.log(`${serverName} running at http://localhost:${port}`);
                console.log(`Static files directory: ${publicDirectory}`);
                runningServers.set(port, server);
                resolve(server);
            }
        });
    });
}

// 从 bundle.json 解析端口配置
function parseServerConfig(bundleJsonPath, mainPackagePath) {
    const config = {
        mainPackage: {
            port: 3000,
            path: mainPackagePath || path.join(BASE_PUBLIC_DIRECTORY, 'main-package')
        },
        bundles: []
    };

    try {
        if (require('fs').existsSync(bundleJsonPath)) {
            const bundleData = JSON.parse(require('fs').readFileSync(bundleJsonPath, 'utf-8'));

            // 解析主包端口（从 bundleServerAddress 提取）
            if (bundleData.bundleServerAddress) {
                try {
                    const mainUrl = new URL(bundleData.bundleServerAddress);
                    const mainPort = parseInt(mainUrl.port, 10) || 3000;
                    config.mainPackage.port = mainPort;
                } catch (error) {
                    console.warn(`Invalid main package URL: ${bundleData.bundleServerAddress}`);
                }
            }

            // 解析每个 bundle 的端口
            if (bundleData.bundles) {
                for (const [bundleName, bundleInfo] of Object.entries(bundleData.bundles)) {
                    if (bundleInfo.url) {
                        try {
                            const bundleUrl = new URL(bundleInfo.url);
                            const bundlePort = parseInt(bundleUrl.port, 10) || 3001;
                            const bundlePath = path.dirname(bundleJsonPath); // 使用 bundle.json 所在目录

                            config.bundles.push({
                                name: bundleName,
                                port: bundlePort,
                                path: bundlePath // bundles 目录路径
                            });
                            console.log(`Parsed bundle ${bundleName}: port ${bundlePort}, path ${bundlePath}`);
                        } catch (error) {
                            console.warn(`Invalid URL for bundle ${bundleName}: ${bundleInfo.url}`);
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error parsing bundle.json:', error);
    }

    return config;
}

// 启动所有服务器
async function startAllServers(bundleJsonPath, mainPackagePath, bundlesPath) {
    const config = parseServerConfig(bundleJsonPath, mainPackagePath);

    console.log('Starting local servers...');
    console.log('Server configuration:', JSON.stringify(config, null, 2));

    try {
        // 启动主包服务器
        await startServer(config.mainPackage.port, config.mainPackage.path, 'Main Package Server');

        // 启动每个 bundle 服务器
        for (const bundle of config.bundles) {
            // 确保 bundle 目录存在
            if (require('fs').existsSync(bundle.path)) {
                await startServer(bundle.port, bundle.path, `Bundle Server (${bundle.name})`);
            } else {
                console.warn(`Bundle directory not found: ${bundle.path}`);
            }
        }

        console.log('\n🎉 All servers started successfully!');
        console.log('Press Ctrl+C to stop all servers');

    } catch (error) {
        console.error('Failed to start servers:', error);
        process.exit(1);
    }
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Bundle Tool Local Server

Usage:
  node server.js [options]

Options:
  --bundle-json <path>     Path to bundle.json file
  --main-package <path>    Path to main package directory
  --bundles <path>         Path to bundles directory
  --help, -h               Show this help message

Examples:
  node server.js --bundle-json ../build/pkw-game/bundles/bundle.json --main-package ../build/pkw-game/main-package --bundles ../build/pkw-game/bundles
        `);
        process.exit(0);
    }

    const bundleJsonPath = args.includes('--bundle-json')
        ? args[args.indexOf('--bundle-json') + 1]
        : path.join(__dirname, '../build/pkw-game/bundles/bundle.json');

    const mainPackagePath = args.includes('--main-package')
        ? args[args.indexOf('--main-package') + 1]
        : path.join(__dirname, '../build/pkw-game/main-package');

    const bundlesPath = args.includes('--bundles')
        ? args[args.indexOf('--bundles') + 1]
        : path.join(__dirname, '../build/pkw-game/bundles');

    startAllServers(bundleJsonPath, mainPackagePath, bundlesPath);
}

// 导出函数供其他模块使用
module.exports = {
    startServer,
    startAllServers,
    killPortProcess,
    parseServerConfig
};