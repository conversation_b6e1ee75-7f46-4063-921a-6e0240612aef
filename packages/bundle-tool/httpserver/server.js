const http = require('http');
const fs = require('fs').promises;
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');

// --- 配置区 ---
const DEFAULT_PORT = 3000;
const BASE_PUBLIC_DIRECTORY = path.join(__dirname, 'public');

const mimeTypes = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm',
    '.ico': 'image/x-icon',
    '.atlas': 'text/plain' // 添加 .atlas 文件的 MIME 类型
};

// 存储运行中的服务器实例
const runningServers = new Map();

// 创建单个服务器实例
function createServer(port, publicDirectory, serverName = 'Server') {
    const server = http.createServer(async (req, res) => {
        try {
            // 解析请求的 URL，去除查询字符串
            const parsedUrl = url.parse(req.url);
            let requestedPath = parsedUrl.pathname === '/' ? '/index.html' : parsedUrl.pathname;

            // 解码 URL 路径，处理可能的编码字符
            requestedPath = decodeURIComponent(requestedPath);

            // 构建文件的绝对路径
            const filePath = path.join(publicDirectory, requestedPath);

            // --- 安全检查：防止目录遍历攻击 ---
            const normalizedPath = path.normalize(filePath);
            if (!normalizedPath.startsWith(publicDirectory)) {
                console.warn(`[SECURITY] Forbidden access attempt: ${req.url}`);
                res.writeHead(403, { 'Content-Type': 'text/plain' });
                res.end('403 Forbidden');
                return;
            }

            // 异步读取文件内容
            const fileContent = await fs.readFile(filePath);

            // 获取文件扩展名以确定 MIME 类型
            const extname = path.extname(filePath).toLowerCase();
            const contentType = mimeTypes[extname] || 'application/octet-stream';

            // 发送成功的响应
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(fileContent);

        } catch (error) {
            // --- 错误处理 ---
            if (error.code === 'ENOENT') {
                console.error(`[404] ${serverName} - File not found: ${req.url}`);
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>');
            } else {
                console.error(`[500] ${serverName} - Server error for ${req.url}:`, error);
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end('<h1>500 Internal Server Error</h1>');
            }
        }
    });

    return server;
}

// 杀死指定端口的进程
async function killPortProcess(port) {
    return new Promise((resolve) => {
        const killCommand = process.platform === 'win32'
            ? `netstat -ano | findstr :${port}`
            : `lsof -ti:${port}`;

        const killProcess = spawn('sh', ['-c', killCommand], { stdio: 'pipe' });

        killProcess.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                const finalKillCommand = process.platform === 'win32'
                    ? `taskkill /PID ${output.split(/\s+/).pop()} /F`
                    : `kill -9 ${output}`;

                spawn('sh', ['-c', finalKillCommand], { stdio: 'inherit' });
                console.log(`Killed process on port ${port}`);
            }
        });

        killProcess.on('close', () => {
            resolve();
        });
    });
}

// 启动服务器
async function startServer(port, publicDirectory, serverName = 'Server') {
    // 先杀死可能占用端口的进程
    await killPortProcess(port);

    // 如果已有服务器在运行，先关闭
    if (runningServers.has(port)) {
        const existingServer = runningServers.get(port);
        existingServer.close();
        runningServers.delete(port);
    }

    const server = createServer(port, publicDirectory, serverName);

    return new Promise((resolve, reject) => {
        server.listen(port, (error) => {
            if (error) {
                console.error(`Failed to start ${serverName} on port ${port}:`, error);
                reject(error);
            } else {
                console.log(`${serverName} running at http://localhost:${port}`);
                console.log(`Static files directory: ${publicDirectory}`);
                runningServers.set(port, server);
                resolve(server);
            }
        });
    });
}

// 获取本地 IP 地址
function getLocalIPAddress() {
    const { networkInterfaces } = require('os');
    const nets = networkInterfaces();

    for (const name of Object.keys(nets)) {
        for (const net of nets[name]) {
            // 跳过内部地址和非 IPv4 地址
            if (net.family === 'IPv4' && !net.internal) {
                // 优先返回 192.168.x.x 地址
                if (net.address.startsWith('192.168.')) {
                    return net.address;
                }
            }
        }
    }

    // 如果没找到 192.168.x.x，返回第一个非内部 IPv4 地址
    for (const name of Object.keys(nets)) {
        for (const net of nets[name]) {
            if (net.family === 'IPv4' && !net.internal) {
                return net.address;
            }
        }
    }

    return '*************'; // 默认值
}

// 自动分配端口并生成服务器配置
function generateServerConfig(bundleJsonPath, mainPackagePath) {
    const localIP = getLocalIPAddress();
    let currentPort = 30001; // 从 30001 开始分配端口

    const config = {
        localIP: localIP,
        mainPackage: {
            port: currentPort++,
            path: mainPackagePath || path.join(BASE_PUBLIC_DIRECTORY, 'main-package'),
            url: `http://${localIP}:${currentPort - 1}/`
        },
        bundles: []
    };

    try {
        console.log(`Parsing bundle config from: ${bundleJsonPath}`);
        console.log(`Local IP address: ${localIP}`);

        if (require('fs').existsSync(bundleJsonPath)) {
            const bundleData = JSON.parse(require('fs').readFileSync(bundleJsonPath, 'utf-8'));

            // 为每个 bundle 分配端口
            if (bundleData.bundles) {
                for (const [bundleName, bundleInfo] of Object.entries(bundleData.bundles)) {
                    const bundlePort = currentPort++;
                    const bundleUrl = `http://${localIP}:${bundlePort}/`;

                    config.bundles.push({
                        name: bundleName,
                        port: bundlePort,
                        path: path.dirname(bundleJsonPath), // bundles 目录路径
                        url: bundleUrl
                    });

                    console.log(`Assigned bundle ${bundleName}: port ${bundlePort}, URL ${bundleUrl}`);
                }
            }
        }
    } catch (error) {
        console.error('Error parsing bundle.json:', error);
    }

    return config;
}

// 更新配置文件中的 URL
function updateConfigFiles(config, bundleJsonPath, mainPackagePath) {
    try {
        // 1. 更新 bundle.json
        if (require('fs').existsSync(bundleJsonPath)) {
            const bundleData = JSON.parse(require('fs').readFileSync(bundleJsonPath, 'utf-8'));

            // 更新主包服务器地址
            bundleData.bundleServerAddress = config.mainPackage.url;

            // 更新每个 bundle 的 URL
            if (bundleData.bundles) {
                for (const bundleConfig of config.bundles) {
                    if (bundleData.bundles[bundleConfig.name]) {
                        bundleData.bundles[bundleConfig.name].url = bundleConfig.url;
                    }
                }
            }

            // 写回 bundle.json
            require('fs').writeFileSync(bundleJsonPath, JSON.stringify(bundleData, null, 2));
            console.log('Updated bundle.json with local URLs');
        }

        // 2. 更新主包的 manifest 文件
        const versionManifestPath = path.join(mainPackagePath, 'version.manifest');
        const projectManifestPath = path.join(mainPackagePath, 'project.manifest');

        // 更新 version.manifest
        if (require('fs').existsSync(versionManifestPath)) {
            const versionData = JSON.parse(require('fs').readFileSync(versionManifestPath, 'utf-8'));

            versionData.packageUrl = config.mainPackage.url;
            versionData.remoteManifestUrl = `${config.mainPackage.url}project.manifest`;
            versionData.remoteVersionUrl = `${config.mainPackage.url}version.manifest`;

            require('fs').writeFileSync(versionManifestPath, JSON.stringify(versionData, null, 2));
            console.log('Updated version.manifest with local URLs');
        }

        // 更新 project.manifest
        if (require('fs').existsSync(projectManifestPath)) {
            const projectData = JSON.parse(require('fs').readFileSync(projectManifestPath, 'utf-8'));

            projectData.packageUrl = config.mainPackage.url;
            projectData.remoteManifestUrl = `${config.mainPackage.url}project.manifest`;
            projectData.remoteVersionUrl = `${config.mainPackage.url}version.manifest`;

            require('fs').writeFileSync(projectManifestPath, JSON.stringify(projectData, null, 2));
            console.log('Updated project.manifest with local URLs');
        }

    } catch (error) {
        console.error('Error updating config files:', error);
    }
}

// 复制文件到端口特定目录
async function copyToPortDirectories(config, mainPackagePath, bundlesPath) {
    const fs = require('fs');

    try {
        // 1. 复制主包到端口目录
        const mainPortDir = path.join(BASE_PUBLIC_DIRECTORY, `port-${config.mainPackage.port}`);
        if (!fs.existsSync(mainPortDir)) {
            fs.mkdirSync(mainPortDir, { recursive: true });
        }

        if (fs.existsSync(mainPackagePath)) {
            await new Promise((resolve, reject) => {
                const copyProcess = spawn('cp', ['-r', `${mainPackagePath}/.`, mainPortDir], { stdio: 'inherit' });
                copyProcess.on('close', (code) => {
                    if (code === 0) {
                        console.log(`Copied main package to ${mainPortDir}`);
                        resolve();
                    } else {
                        reject(new Error(`Copy failed with code ${code}`));
                    }
                });
            });
        }

        // 2. 复制每个 bundle 到对应端口目录
        for (const bundle of config.bundles) {
            const bundlePortDir = path.join(BASE_PUBLIC_DIRECTORY, `port-${bundle.port}`);
            if (!fs.existsSync(bundlePortDir)) {
                fs.mkdirSync(bundlePortDir, { recursive: true });
            }

            // 复制整个 bundles 目录内容到 bundle 端口目录
            if (fs.existsSync(bundlesPath)) {
                await new Promise((resolve, reject) => {
                    const copyProcess = spawn('cp', ['-r', `${bundlesPath}/.`, bundlePortDir], { stdio: 'inherit' });
                    copyProcess.on('close', (code) => {
                        if (code === 0) {
                            console.log(`Copied bundles to ${bundlePortDir} for bundle ${bundle.name}`);
                            resolve();
                        } else {
                            reject(new Error(`Copy failed with code ${code}`));
                        }
                    });
                });
            }
        }

    } catch (error) {
        console.error('Error copying files to port directories:', error);
        throw error;
    }
}

// 启动所有服务器
async function startAllServers(bundleJsonPath, mainPackagePath, bundlesPath) {
    const config = generateServerConfig(bundleJsonPath, mainPackagePath);

    console.log('Starting local servers...');
    console.log('Server configuration:', JSON.stringify(config, null, 2));

    try {
        // 1. 更新配置文件中的 URL
        updateConfigFiles(config, bundleJsonPath, mainPackagePath);

        // 2. 复制文件到端口特定目录
        await copyToPortDirectories(config, mainPackagePath, bundlesPath);

        // 3. 启动主包服务器
        const mainPortDir = path.join(BASE_PUBLIC_DIRECTORY, `port-${config.mainPackage.port}`);
        await startServer(config.mainPackage.port, mainPortDir, 'Main Package Server');

        // 4. 启动每个 bundle 服务器
        for (const bundle of config.bundles) {
            const bundlePortDir = path.join(BASE_PUBLIC_DIRECTORY, `port-${bundle.port}`);
            if (require('fs').existsSync(bundlePortDir)) {
                await startServer(bundle.port, bundlePortDir, `Bundle Server (${bundle.name})`);
            } else {
                console.warn(`Bundle port directory not found: ${bundlePortDir}`);
            }
        }

        console.log('\n🎉 All servers started successfully!');
        console.log(`Main Package: ${config.mainPackage.url}`);
        config.bundles.forEach(bundle => {
            console.log(`Bundle ${bundle.name}: ${bundle.url}`);
        });
        console.log('Press Ctrl+C to stop all servers');

    } catch (error) {
        console.error('Failed to start servers:', error);
        process.exit(1);
    }
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Bundle Tool Local Server

Usage:
  node server.js [options]

Options:
  --bundle-json <path>     Path to bundle.json file
  --main-package <path>    Path to main package directory
  --bundles <path>         Path to bundles directory
  --help, -h               Show this help message

Examples:
  node server.js --bundle-json ../build/pkw-game/bundles/bundle.json --main-package ../build/pkw-game/main-package --bundles ../build/pkw-game/bundles
        `);
        process.exit(0);
    }

    const bundleJsonPath = args.includes('--bundle-json')
        ? args[args.indexOf('--bundle-json') + 1]
        : path.join(__dirname, '../build/pkw-game/bundles/bundle.json');

    const mainPackagePath = args.includes('--main-package')
        ? args[args.indexOf('--main-package') + 1]
        : path.join(__dirname, '../build/pkw-game/main-package');

    const bundlesPath = args.includes('--bundles')
        ? args[args.indexOf('--bundles') + 1]
        : path.join(__dirname, '../build/pkw-game/bundles');

    startAllServers(bundleJsonPath, mainPackagePath, bundlesPath);
}

// 导出函数供其他模块使用
module.exports = {
    startServer,
    startAllServers,
    killPortProcess,
    generateServerConfig,
    updateConfigFiles,
    copyToPortDirectories
};