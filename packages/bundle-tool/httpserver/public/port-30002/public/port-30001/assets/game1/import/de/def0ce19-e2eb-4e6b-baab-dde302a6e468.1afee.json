[1, ["ecpdLyjvZBwrvm+cedCcQy", "1cafLKAJRIQ7WD5mIW7IlJ", "7a/QZLET9IDreTiBfRn2PD", "72R2uDLhlMcoASfFYMRc8p", "2bnT9lwIdDt5n4fRYThnaj"], ["node", "_spriteFrame", "_N$skeletonData", "_normalMaterial", "actor", "background", "scene", "_parent"], [["cc.Node", ["_name", "_id", "_obj<PERSON><PERSON>s", "_components", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint"], 0, 9, 5, 7, 1, 2, 5], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Scene", ["_name", "_active", "autoReleaseAssets", "_children", "_anchorPoint", "_trs"], 0, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "node"], 2, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["867e6kHo11EMLMMrEP2aOwV", ["node", "background", "actor"], 3, 1, 1, 1], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], 0, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_normalMaterial"], 2, 1, 9, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["cc.Widget", ["_alignFlags", "node"], 2, 1]], [[0, 0, 6, 3, 4, 5, 2], [4, 0, 1, 2, 1], [1, 0, 1, 3], [2, 0, 1, 2, 3, 4, 5, 4], [0, 0, 1, 7, 3, 4, 5, 3], [0, 0, 2, 6, 3, 4, 5, 3], [0, 0, 6, 7, 3, 2], [0, 0, 6, 7, 3, 4, 8, 5, 2], [0, 0, 6, 7, 3, 4, 5, 2], [3, 0, 1, 2, 3], [5, 0, 1, 2], [6, 0, 1, 2, 3, 4, 5, 6, 5], [7, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 4], [9, 0, 1, 2, 3, 2], [10, 0, 1, 2, 3], [11, 0, 1, 1], [12, 0, 1, 2]], [[2, "game1", {}], [4, "<PERSON><PERSON>", "a5esZu+45LA5mBpvttspPD", [-3, -4, -5], [[16, -1, [5, 512, 400]], [17, 45, -2]], [5, 512, 400], [256, 200, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Game1Stage", 1, [-9, -10], [[12, -8, -7, -6]]], [7, "bg node", 2, [-12, -13], [[10, 1, -11]], [5, 1024, 400], [0, 0, 0.5], [-256, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "ReturnButton", 1, [-16], [[1, -14, [7], 8], [14, 1, -15, [[15, "867e6kHo11EMLMMrEP2aOwV", "onClickSceneBack", 2]], 9]], [5, 244, 94], [0, -146.875, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [0, "ractor", 2, [[11, "default", "walk", 0, "walk", -17, [4], 5]], [5, 1223.72, 1055.62], [-124.865, -102.283, 0, 0, 0, 0, 1, 0.2, 0.2, 1]], [3, "New Node", false, true, [1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Main Camera", 512, 1, [[9, 7, -1, -18]], [5, 960, 640], [0, 0, 412.2280922013928, 0, 0, 0, 1, 1, 1, 1]], [0, "New Node", 3, [[1, -19, [0], 1]], [5, 1024, 400], [512, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "New Node copy", 3, [[1, -20, [2], 3]], [5, 1024, 400], [1536, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "New Label", 4, [[13, "返回", 1, 1, -21, [6]]], [5, 80, 50.4], [0, 6.275, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, -3, 4, 0, 4, 5, 0, 5, 3, 0, 0, 2, 0, -1, 3, 0, -2, 5, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, -1, 10, 0, 0, 5, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 6, 6, 1, 7, 6, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 2, -1, -1, 1, 3], [0, 1, 0, 1, 2, 3, 0, 0, 4, 0]]