[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8Jzu+RgRI0KlC/GNkOKXv", "6fAve3cJlI8pwQIccX/UX1", "28OM4LWVdBoqoJUBkS/QXE", "b1kEBV1QVNsb34TANy/fGA", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "97Fbr+BO9IwLw2m6ZCNc1R"], ["node", "_spriteFrame", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "txtOnline", "txtScore", "txtScoreWord", "txtLevelWord", "txtEnterWord", "btnEnter", "imgOnline", "imgScore", "imgHot", "data", "_parent"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 1, 1, 2, 4, 5, 7, 5, 5, 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "_left", "_right", "node"], -3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 5, 5, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_children", "_components", "_prefab", "_contentSize", "_trs"], 1, 2, 9, 4, 5, 7], ["cdab4bl6XBO9Yrrh0jSWook", ["node", "imgHot", "imgScore", "imgOnline", "btnEnter", "txtEnterWord", "txtLevelWord", "txtScoreWord", "txtScore", "txtOnline"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[3, 0, 1, 2], [0, 0, 2, 3, 4, 5, 6, 2], [7, 0, 1, 2, 3, 4, 5, 6, 3], [0, 0, 2, 3, 4, 7, 5, 8, 6, 2], [1, 0, 1, 3, 4, 3], [2, 0, 2, 4, 1, 8, 9, 5], [2, 0, 3, 1, 8, 9, 4], [6, 0, 2], [0, 0, 2, 9, 3, 4, 5, 6, 2], [0, 0, 1, 2, 3, 4, 7, 5, 3], [1, 2, 0, 3, 4, 5, 3], [1, 3, 4, 1], [1, 2, 0, 1, 3, 4, 5, 4], [3, 1, 1], [2, 0, 2, 5, 3, 1, 6, 7, 8, 9, 8], [4, 2, 0, 1, 3, 6, 5], [4, 0, 4, 5, 1, 6, 5], [5, 0, 2, 3, 4, 5, 6, 2], [5, 1, 0, 2, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1]], [[7, "RoomListItem"], [2, "RoomListItem", 512, [-15, -16, -17, -18, -19, -20, -21, -22], [[16, 40, 342.5, 342.5, 1011, -2], [12, 1, 0, false, -3, [10], 11], [18, 1.03, 3, -4], [19, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5]], [13, -1], [5, 395, 216], [540, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "Background", 512, [-25], [[10, 1, 0, -23, [7], 8], [15, 0, 45, 100, 40, -24]], [0, "4dCg4Pln1GFrQguvAKR8mY", 1], [5, 100, 40], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "button_enter", 1, [2], [-26], [0, "a73OQUZKVMLr/9FOnuuSld", 1], [5, 100, 40], [127.992, 13.195, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_score", 1, [-27], [0, "97XJ0XA0FIQZ+kerugZSEM", 1], [5, 37, 37], [-117, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 2, false, 4, [0]], [1, "icon_online", 1, [-28], [0, "acEq+fz4dIQIbQj+nuWu0N", 1], [5, 36, 35], [-8.226, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 2, false, 6, [1]], [3, "txt_score", 1, [-29], [0, "852LZ8VhdERJDLhxTmKNIT", 1], [4, 4294567144], [5, 19.47, 44.1], [0, 0, 0.5], [-86, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "0", 35, 0, 1, 8, [2]], [3, "txt_online", 1, [-30], [0, "5d81ZvR1RF5aw+hD4OP6K+", 1], [4, 4294567144], [5, 19.47, 44.1], [0, 0, 0.5], [19, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "0", 35, 0, 1, 10, [3]], [1, "txt_level_word", 1, [-31], [0, "55piLP1e1NK6telaiFjnuS", 1], [5, 80, 50.4], [-98.901, 41.714, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "初级", 1, 1, 12, [4]], [1, "txt_score_word", 1, [-32], [0, "4eNjQH4L1P7IkqmqLF7hBW", 1], [5, 80, 50.4], [122.608, -46.987, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "底分", 1, 1, 14, [5]], [9, "txt", 512, 2, [-33], [0, "76BFzO40dCsInLfJPuO5Pe", 1], [4, 4278190080], [5, 100, 40]], [14, "进入", 20, false, 1, 1, 1, 1, 16, [6]], [17, 3, 3, [4, 4293322470], [4, 4291348680], [4, 3363338360], 2], [1, "icon_hot", 1, [-34], [0, "8b39uSZ5JE8rIxznkNvypJ", 1], [5, 62, 68], [151, 71.382, 0, 0, 0, 0, 1, 1, 1, 1]], [11, 19, [9]]], 0, [0, 6, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 7, 11, 0, 8, 9, 0, 9, 15, 0, 10, 13, 0, 11, 17, 0, 12, 18, 0, 13, 7, 0, 14, 5, 0, 15, 20, 0, 0, 1, 0, -1, 4, 0, -2, 6, 0, -3, 8, 0, -4, 10, 0, -5, 12, 0, -6, 14, 0, -7, 3, 0, -8, 19, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -1, 18, 0, -1, 5, 0, -1, 7, 0, -1, 9, 0, -1, 11, 0, -1, 13, 0, -1, 15, 0, -1, 17, 0, -1, 20, 0, 16, 1, 2, 17, 3, 34], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 7, 18, 18, 18, 18, 20], [-1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, 1, 1, 2, 3, 4, 5, 1], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 2, 3, 4, 1, 5, 6, 7, 8]]