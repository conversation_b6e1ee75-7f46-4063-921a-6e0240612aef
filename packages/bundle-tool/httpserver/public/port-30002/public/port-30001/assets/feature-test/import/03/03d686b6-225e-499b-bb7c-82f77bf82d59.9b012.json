[1, ["ecpdLyjvZBwrvm+cedCcQy", "f0BIwQ8D5Ml7nTNQbh1YlS", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO", "ffDpHHVcZAhqOfy25Fe4w7"], ["node", "_spriteFrame", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_parent", "root", "prizeIndex", "currencyType", "awardType", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_children", "_trs", "_parent", "_anchorPoint", "_color"], 1, 9, 4, 5, 2, 7, 1, 5, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], 0, 1, 12, 4, 5, 5, 7, 5], ["cc.Label", ["_string", "_fontSize", "_enableWrapText", "_N$verticalAlign", "_N$overflow", "_N$horizontalAlign", "_N$cacheMode", "_lineHeight", "node", "_materials"], -5, 1, 3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["alignMode", "_alignFlags", "_originalWidth", "_originalHeight", "_left", "node"], -2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.EditBox", ["_string", "max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 0, 1, 1, 1, 1], ["52525Y5Px5BhadXJNXT8WZE", ["node", "awardType", "currencyType", "prizeIndex"], 3, 1, 1, 1, 1]], [[3, 0, 1, 2], [5, 0, 1, 2, 3, 5, 5], [0, 0, 7, 5, 2, 3, 4, 6, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 1, 7, 2, 3, 9, 4, 3], [2, 0, 1, 2, 5, 3, 4, 6, 8, 9, 8], [4, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [9, 0, 1, 2, 3], [2, 0, 1, 7, 2, 3, 4, 8, 9, 7], [5, 0, 1, 4, 2, 3, 5, 6], [7, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 3, 4, 5, 6, 3], [1, 0, 1, 3, 4, 5, 6, 7, 8, 3], [1, 0, 1, 2, 3, 4, 5, 9, 6, 7, 8, 4], [4, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3, 4, 5, 6, 4], [6, 0, 2], [0, 0, 5, 2, 3, 4, 8, 6, 2], [3, 1, 1], [11, 0, 1, 2, 3, 1]], [[17, "TestLuckTurntable"], [18, "TestLuckTurntable", [-6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[20, -5, -4, -3, -2]], [19, -1], [5, 260, 270], [0, 0.5, 1], [609.278, 374.227, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Background", 512, [-18], [[6, 1, 0, -16, [1], 2], [1, 0, 45, 100, 40, -17]], [0, "d1nDkZdRdAIZO8zg+zldkH", 1], [5, 100, 40]], [3, "Background", 512, [-21], [[6, 1, 0, -19, [8], 9], [1, 0, 45, 100, 40, -20]], [0, "2fp66Xs/pHqY986NQVLm8z", 1], [5, 100, 40]], [3, "Background", 512, [-24], [[6, 1, 0, -22, [15], 16], [1, 0, 45, 100, 40, -23]], [0, "64R3rntFlLs6uoSxGhNel4", 1], [5, 100, 40]], [3, "Background", 512, [-27], [[6, 1, 0, -25, [22], 23], [1, 0, 45, 100, 40, -26]], [0, "b65p98juxGoJLdcd2fnR0m", 1], [5, 100, 40]], [3, "Background", 512, [-30], [[6, 1, 0, -28, [29], 30], [1, 0, 45, 100, 40, -29]], [0, "7fxjnGuVZGUqCKYqElBwNu", 1], [5, 100, 40]], [3, "Background", 512, [-33], [[6, 1, 0, -31, [36], 37], [1, 0, 45, 100, 40, -32]], [0, "e1Iyb8QApPaKeNT8FnLk6h", 1], [5, 100, 40]], [3, "Background", 512, [-36], [[6, 1, 0, -34, [43], 44], [1, 0, 45, 100, 40, -35]], [0, "3d4fwQ91lHwbeHbw/VJi+d", 1], [5, 100, 40]], [11, "AwardType", 1, [-38, -39, -40], [-37], [0, "b8k1eOpYlB8JqujBzSIjTs", 1], [5, 60, 40], [-63, -234.991, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "CurrencyType", 1, [-42, -43, -44], [-41], [0, "e8jV+oaEFEcKtU8S5hsT3k", 1], [5, 60, 40], [17, -234.991, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "PrizeIndex", 1, [-46, -47, -48], [-45], [0, "0drTY4B6FDH5upHvkF7zkR", 1], [5, 60, 40], [97, -234.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Start", 1, [2], [[7, 2, -49, [[8, "52525Y5Px5BhadXJNXT8WZE", "testStartTime", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 2, 3, 4, 5, 6]], [0, "26PY3mfFNDPZL38Ouwj1A3", 1], [5, 100, 40], [-63, -34.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "End", 1, [3], [[7, 2, -50, [[8, "52525Y5Px5BhadXJNXT8WZE", "testEndTime", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 3, 10, 11, 12, 13]], [0, "e5tMeli4pKNZqCWOPHJ0R6", 1], [5, 100, 40], [57, -34.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Ready", 1, [4], [[7, 2, -51, [[8, "52525Y5Px5BhadXJNXT8WZE", "testReady", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 4, 17, 18, 19, 20]], [0, "07nIhhGsRE7rjLq5+gcGct", 1], [5, 100, 40], [-63, -84.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Countdown", 1, [5], [[7, 2, -52, [[8, "52525Y5Px5BhadXJNXT8WZE", "testCountdown", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 5, 24, 25, 26, 27]], [0, "ac3r3kJK9GCZiyRs17dNue", 1], [5, 100, 40], [57, -84.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Over", 1, [6], [[7, 2, -53, [[8, "52525Y5Px5BhadXJNXT8WZE", "testOver", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 6, 31, 32, 33, 34]], [0, "e932aY1hxKZqbmvQ4CvQLJ", 1], [5, 100, 40], [57, -134.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Draw", 1, [7], [[7, 2, -54, [[8, "52525Y5Px5BhadXJNXT8WZE", "testDraw", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 7, 38, 39, 40, 41]], [0, "b8h7hll0FCIZww2XaE5nCs", 1], [5, 100, 40], [-63, -134.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Result", 1, [8], [[7, 2, -55, [[8, "52525Y5Px5BhadXJNXT8WZE", "testResult", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 8, 45, 46, 47, 48]], [0, "902+qLH1RIapWX6uPZrvG2", 1], [5, 100, 40], [-63, -184.991, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "BACKGROUND_SPRITE", 512, 9, [[-56, [1, 0, 45, 160, 40, -57]], 1, 4], [0, "d80gqGtHpKTbROLqaIPXR7", 1], [5, 60, 40]], [13, "TEXT_LABEL", 512, 9, [[-58, [10, 0, 45, 2, 158, 40, -59]], 1, 4], [0, "66HjCirAxNN5oBS18lkN2I", 1], [5, 58, 40], [0, 0, 1], [-28, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "PLACEHOLDER_LABEL", 512, false, 9, [[-60, [10, 0, 45, 2, 158, 40, -61]], 1, 4], [0, "185cKNxaZFAZj2PWz42b8I", 1], [4, 4290493371], [5, 58, 40], [0, 0, 1], [-28, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "BACKGROUND_SPRITE", 512, 10, [[-62, [1, 0, 45, 160, 40, -63]], 1, 4], [0, "5eNvSb8spJD5G/00qCg0bT", 1], [5, 60, 40]], [13, "TEXT_LABEL", 512, 10, [[-64, [10, 0, 45, 2, 158, 40, -65]], 1, 4], [0, "b2rYMxMaxEsrWSeJdWmcKo", 1], [5, 58, 40], [0, 0, 1], [-28, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "PLACEHOLDER_LABEL", 512, false, 10, [[-66, [10, 0, 45, 2, 158, 40, -67]], 1, 4], [0, "1eylU7kLdIAp/JhH/sIoCL", 1], [4, 4290493371], [5, 58, 40], [0, 0, 1], [-28, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "BACKGROUND_SPRITE", 512, 11, [[-68, [1, 0, 45, 160, 40, -69]], 1, 4], [0, "67hPc4xthGpaXAeVjnOEPZ", 1], [5, 60, 40]], [13, "TEXT_LABEL", 512, 11, [[-70, [10, 0, 45, 2, 158, 40, -71]], 1, 4], [0, "1cVr6h4npGeYpbFOesYbI2", 1], [5, 58, 40], [0, 0, 1], [-28, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "PLACEHOLDER_LABEL", 512, false, 11, [[-72, [10, 0, 45, 2, 158, 40, -73]], 1, 4], [0, "554AyvAtZDfqegvPjYTg93", 1], [4, 4290493371], [5, 58, 40], [0, 0, 1], [-28, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label", 512, 2, [[5, "Start", 20, false, 1, 1, 1, 1, -74, [0]]], [0, "46xfzxqE9Pk7GK2MxeEBuF", 1], [4, 4278190080], [5, 100, 40]], [4, "Label", 512, 3, [[5, "End", 20, false, 1, 1, 1, 1, -75, [7]]], [0, "53oMzqAglCwItwbwxqXgSN", 1], [4, 4278190080], [5, 100, 40]], [4, "Label", 512, 4, [[5, "Ready", 20, false, 1, 1, 1, 1, -76, [14]]], [0, "9284pBYzRBLYBb8YPbzKnS", 1], [4, 4278190080], [5, 100, 40]], [4, "Label", 512, 5, [[5, "Countdown", 20, false, 1, 1, 1, 1, -77, [21]]], [0, "a1A8CfS1JM17MRPcRueLOx", 1], [4, 4278190080], [5, 100, 40]], [4, "Label", 512, 6, [[5, "Over", 20, false, 1, 1, 1, 1, -78, [28]]], [0, "4dVLlij81MkJqxroT1Jk45", 1], [4, 4278190080], [5, 100, 40]], [4, "Label", 512, 7, [[5, "Draw", 20, false, 1, 1, 1, 1, -79, [35]]], [0, "80VTYKtLxMsYBrmqTf/XlQ", 1], [4, 4278190080], [5, 100, 40]], [4, "Label", 512, 8, [[5, "Result", 20, false, 1, 1, 1, 1, -80, [42]]], [0, "51BENPpRxKGoXkdQvfKYFv", 1], [4, 4278190080], [5, 100, 40]], [15, 1, 0, 19, [49]], [9, "0", 20, 25, false, 1, 1, 20, [50]], [9, "Enter text here...", 20, 25, false, 1, 1, 21, [51]], [16, "0", 8, 6, 9, 36, 37, 35], [15, 1, 0, 22, [52]], [9, "0", 20, 25, false, 1, 1, 23, [53]], [9, "Enter text here...", 20, 25, false, 1, 1, 24, [54]], [16, "0", 8, 6, 10, 40, 41, 39], [15, 1, 0, 25, [55]], [9, "2", 20, 25, false, 1, 1, 26, [56]], [9, "Enter text here...", 20, 25, false, 1, 1, 27, [57]], [16, "2", 8, 6, 11, 44, 45, 43]], 0, [0, 7, 1, 0, 8, 46, 0, 9, 42, 0, 10, 38, 0, 0, 1, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, -6, 17, 0, -7, 18, 0, -8, 9, 0, -9, 10, 0, -10, 11, 0, 0, 2, 0, 0, 2, 0, -1, 28, 0, 0, 3, 0, 0, 3, 0, -1, 29, 0, 0, 4, 0, 0, 4, 0, -1, 30, 0, 0, 5, 0, 0, 5, 0, -1, 31, 0, 0, 6, 0, 0, 6, 0, -1, 32, 0, 0, 7, 0, 0, 7, 0, -1, 33, 0, 0, 8, 0, 0, 8, 0, -1, 34, 0, -1, 38, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -1, 42, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, -1, 46, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, -1, 35, 0, 0, 19, 0, -1, 36, 0, 0, 20, 0, -1, 37, 0, 0, 21, 0, -1, 39, 0, 0, 22, 0, -1, 40, 0, 0, 23, 0, -1, 41, 0, 0, 24, 0, -1, 43, 0, 0, 25, 0, -1, 44, 0, 0, 26, 0, -1, 45, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 11, 1, 2, 6, 12, 3, 6, 13, 4, 6, 14, 5, 6, 15, 6, 6, 16, 7, 6, 17, 8, 6, 18, 80], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 39, 43], [-1, -1, 1, 2, 3, 4, 5, -1, -1, 1, 2, 3, 4, 5, -1, -1, 1, 2, 3, 4, 5, -1, -1, 1, 2, 3, 4, 5, -1, -1, 1, 2, 3, 4, 5, -1, -1, 1, 2, 3, 4, 5, -1, -1, 1, 2, 3, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 1, 1], [0, 0, 1, 1, 2, 1, 3, 0, 0, 1, 1, 2, 1, 3, 0, 0, 1, 1, 2, 1, 3, 0, 0, 1, 1, 2, 1, 3, 0, 0, 1, 1, 2, 1, 3, 0, 0, 1, 1, 2, 1, 3, 0, 0, 1, 1, 2, 1, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4]]