[1, ["ecpdLyjvZBwrvm+cedCcQy", "f0BIwQ8D5Ml7nTNQbh1YlS", "e97GVMl6JHh5Ml5qEDdSGa", "9bvaMerUlDyary99mJa6xp", "eenNHTfzRGvKM5VczNOD4I", "29FYIk+N1GYaeWH/q1NxQO", "6d/8/IeTpCs5xQCQAJyVzo", "48WgNLZ8lI9pDAEtxAzAlz", "b6PrgdlndHMZXuoWgQ742T", "a0csF7N1lIwYYbKxWgX2l/", "ffDpHHVcZAhqOfy25Fe4w7", "f6QognJBFBg6rywutBuYpy", "d4qyiLJZ9FF70TaFyZ8V+m", "4es9F40StA2IzDUHByQkZR", "68J8oyAQdFUrqy37MXmbtE", "5f5dyqtRNNxaFmVzYns6FZ", "64RN/nL3FIq7owk+FvMjme", "90AErWL21A4ZPvtxQ3XG8G", "5cO7kybDxGj4ipyMYdRYZB", "8e9YY+t5hHKpcNhV172V6h"], ["node", "_spriteFrame", "_parent", "root", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_N$target", "bundleDownloadControl", "cannotDownloadSprite", "downloadFailSprite", "needDownloadSprite", "gameNameLabel", "h5BundleManifest", "androidBundleManifest", "iosBundleManifest", "asset", "roomListItemPrefab", "miniGamePanel", "loginPanel", "roomListControl", "autoLoginLabel", "autoLoginToggle", "loginButton", "passwordEditBox", "usernameEditBox", "target", "scene", "_scrollView"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_id", "_opacity", "_prefab", "_components", "_contentSize", "_children", "_parent", "_trs", "_color", "_anchorPoint"], -2, 4, 9, 5, 2, 1, 7, 5, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], 0, 1, 2, 4, 5, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_anchorPoint", "_children", "_color"], 0, 12, 4, 5, 1, 7, 5, 2, 5], ["cc.Label", ["_fontSize", "_N$verticalAlign", "_string", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "clickEvents"], 2, 1, 5, 5, 5, 1, 6, 6, 6, 6, 9], ["cc.Layout", ["_N$layoutType", "_N$spacingY", "_N$paddingTop", "_N$paddingLeft", "_N$spacingX", "_resize", "_N$paddingBottom", "node", "_layoutSize"], -4, 1, 5], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.EditBox", ["_string", "max<PERSON><PERSON><PERSON>", "_N$inputMode", "_N$inputFlag", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], -1, 1, 1, 1, 1], ["aea7epIr11IRpWxOhq9G706", ["gameName", "bundle", "gameId", "node", "gameNameLabel", "needDownloadSprite", "downloadFailSprite", "cannotDownloadSprite", "bundleDownloadControl"], 0, 1, 1, 1, 1, 1, 1], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.<PERSON>", ["_fitWidth", "node", "_designResolution"], 2, 1, 5], ["6c9c7tXZ1tHSLspEOnPy0BU", ["node", "h5BundleManifest", "androidBundleManifest", "iosBundleManifest"], 3, 1, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkMark"], 2, 1, 5, 1, 1], ["aa5461LSq5Mr6EBXJc0/jvc", ["platform", "node", "usernameEditBox", "passwordEditBox", "loginButton", "miniGamePanel", "autoLoginToggle", "autoLoginLabel"], 2, 1, 1, 1, 1, 1, 1, 1], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -1, 1, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["66fcdQxu0dEU6jSa0U20wrG", ["node", "progressBar", "barText", "loadingPanel"], 3, 1, 1, 1, 1], ["10f659LsiJCs6X3ohKgNi/h", ["node", "progressBar"], 3, 1, 1], ["62c118m6DBL1agLPbw0bMd1", ["node", "scrollView"], 3, 1, 1], ["c1f54m7435HtLfNeaijuCfD", ["node", "roomListControl"], 3, 1, 1], ["fbfc110zNpLv68CPa39Whm6", ["node"], 3, 1], ["d4a47U2kGxJsr6bc/k01Ofa", ["node", "loginPanel", "miniGamePanel"], 3, 1, 1, 1]], [[8, 0, 2, 2], [3, 1, 0, 3, 4, 5, 3], [1, 0, 2, 3, 4, 5, 6, 3], [3, 3, 4, 1], [2, 3, 0, 2, 1, 8, 5], [5, 2, 0, 5, 3, 1, 6, 7, 8, 9, 8], [4, 0, 9, 3, 4, 5, 7, 2], [0, 0, 9, 8, 5, 2], [0, 0, 1, 9, 8, 6, 5, 7, 3], [1, 0, 3, 8, 4, 5, 6, 7, 2], [1, 0, 3, 4, 5, 6, 7, 2], [1, 0, 1, 3, 4, 5, 6, 9, 7, 3], [2, 3, 0, 4, 2, 1, 8, 6], [5, 2, 0, 4, 5, 1, 6, 8, 9, 7], [0, 0, 1, 8, 6, 5, 7, 3], [0, 0, 1, 9, 6, 5, 11, 7, 3], [0, 0, 9, 8, 5, 10, 2], [1, 0, 1, 3, 4, 5, 10, 6, 3], [1, 0, 2, 3, 8, 4, 5, 7, 3], [4, 0, 6, 9, 3, 4, 5, 7, 2], [2, 0, 2, 1, 8, 4], [3, 1, 0, 3, 4, 3], [3, 0, 3, 4, 2], [3, 0, 3, 4, 5, 2], [5, 0, 4, 3, 1, 8, 9, 5], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 2], [23, 0, 1, 2, 3, 3], [24, 0, 1, 2, 3, 1], [25, 0, 1, 1], [0, 0, 9, 8, 6, 5, 7, 10, 2], [4, 0, 1, 6, 3, 4, 5, 3], [4, 0, 1, 6, 3, 4, 5, 8, 7, 3], [4, 0, 1, 2, 6, 3, 4, 10, 5, 8, 7, 4], [2, 0, 8, 2], [2, 0, 4, 5, 6, 7, 2, 1, 8, 8], [16, 0, 1, 2, 3], [22, 0, 1, 2, 2], [10, 0, 2, 1, 3, 4, 5, 6, 7, 8, 4], [11, 0, 1, 3], [12, 0, 1, 2, 3, 4, 3], [0, 0, 3, 8, 6, 7, 10, 3], [0, 0, 9, 6, 7, 2], [0, 0, 3, 8, 6, 5, 10, 3], [0, 0, 9, 6, 5, 2], [0, 0, 8, 6, 5, 2], [0, 0, 8, 6, 5, 11, 7, 2], [0, 0, 1, 9, 6, 5, 7, 10, 3], [0, 0, 2, 8, 6, 5, 11, 7, 3], [0, 0, 9, 6, 5, 11, 7, 2], [0, 0, 1, 8, 6, 5, 11, 7, 12, 10, 3], [0, 0, 1, 9, 8, 6, 5, 7, 10, 3], [0, 0, 8, 6, 5, 11, 7, 10, 2], [0, 0, 2, 9, 5, 3], [0, 0, 1, 9, 6, 5, 7, 12, 10, 3], [0, 0, 2, 4, 9, 8, 6, 5, 7, 4], [1, 0, 1, 3, 4, 5, 6, 3], [1, 0, 2, 3, 8, 4, 5, 6, 7, 3], [4, 0, 1, 6, 9, 3, 4, 5, 8, 7, 3], [13, 0, 1, 2, 3], [14, 0, 1, 2, 2], [2, 0, 5, 2, 1, 8, 5], [2, 3, 0, 4, 1, 8, 5], [15, 0, 1, 2, 3, 1], [8, 0, 1, 2, 3, 3], [8, 2, 1], [3, 3, 4, 5, 1], [3, 0, 2, 3, 4, 3], [5, 2, 0, 3, 1, 8, 9, 5], [9, 0, 1, 2, 4, 5, 6, 7, 4], [9, 0, 1, 3, 2, 4, 5, 6, 7, 5], [6, 0, 1, 10, 2, 3, 4, 5, 2], [6, 0, 1, 10, 2, 3, 4, 5, 6, 7, 8, 9, 2], [6, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [17, 0, 1, 2, 3, 4, 2], [7, 0, 2, 1, 7, 8, 4], [7, 0, 3, 2, 4, 1, 7, 8, 6], [7, 5, 0, 6, 1, 7, 8, 5], [7, 0, 7, 8, 2], [18, 0, 1, 2, 3, 4, 5, 6, 7, 2], [19, 0, 1, 2, 2], [20, 0, 1, 2, 3, 4, 5, 6, 5], [21, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 3, 4, 5, 6, 7, 8, 3], [26, 0, 1, 1], [27, 0, 1, 1], [28, 0, 1], [29, 0, 1, 2, 1]], [[38, "Lobby", null], [42, "PersistRoot", "26xBUW3+ZIXLAeDIi9/MQm", [-6, -7], [[85, -2], [86, -5, -4, -3]], [64, -1], [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "GameListItemCowboy", [-18, -19, -20], [[[25, 2, -10, [4, 4293322470], [4, 4291348680], [4, 3363338360], -9, 32, 33, 34, 35], [37, "Cowboy", 10, "cowboy", -16, -15, -14, -13, -12, -11], -17], 4, 4, 1], [0, "21NoKOEtNAKL7R4Sd2dci5", -8], [5, 250, 140], [-129, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "GameListItemHumanboy", [-31, -32, -33], [[[25, 2, -23, [4, 4293322470], [4, 4291348680], [4, 3363338360], -22, 46, 47, 48, 49], [82, "HumanBoy", "humanboy", -29, -28, -27, -26, -25, -24], -30], 4, 4, 1], [0, "9fVxo0kUJKgaQpH23fVeFp", -21], [5, 250, 140], [131, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "GameListItemPokerMaster", [-44, -45, -46], [[[25, 2, -36, [4, 4293322470], [4, 4291348680], [4, 3363338360], -35, 60, 61, 62, 63], [37, "PokerMaster", 70, "poker-master", -42, -41, -40, -39, -38, -37], -43], 4, 4, 1], [0, "73VHJV/0BOWr/yNdc4QLXg", -34], [5, 250, 140], [-129, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "GameListView", [-50, -51], [[[1, 1, 0, -48, [68], 69], -49], 4, 1], [0, "3dZYbaMMtBfb26Au7/Dokj", -47], [5, 580, 780], [-0.523, -29.626, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "MininGamePanel", false, [-54, 5, -55, -56, -57], [[84, -53, -52]], [0, "69qWsmS9FH653cy06iAAEv", 1], [4, 4284769380], [5, 600, 900]], [45, "layout", [-61, -62, -63, -64], [[1, 1, 0, -58, [16], 17], [74, 2, 10, 10, -59, [5, 400, 200]], [33, 18, -60]], [0, "e37ZKXy6hG1aUfOmPOSxah", 1], [4, 4284111964], [5, 400, 200]], [18, "ProgressBar", false, 2, [-67], [-66], [0, "1184C1lIZDf6jeqRBWfYo4", -65], [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "ProgressBar", false, 3, [-70], [-69], [0, "1184C1lIZDf6jeqRBWfYo4", -68], [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "ProgressBar", false, 4, [-73], [-72], [0, "1184C1lIZDf6jeqRBWfYo4", -71], [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "RoomListView", false, 6, [-76], [-75], [0, "4fFFA7Wk1L35JexYlf9XOt", -74], [5, 1920, 1080], [0, -94.795, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "layout", [2, 3, 4], [[1, 1, 0, -77, [64], 65], [75, 3, 30, 20, 10, 20, -78, [5, 568, 780]], [20, 45, 200, 150, -79]], [0, "d4msB0q4tNsaLOlKDei1ag", 5], [4, 4286611584], [5, 568, 780], [0, -390, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "LoginPanel", [7], [[78, 1, -85, -84, -83, -82, 6, -81, -80]], [0, "deWZ4EiVFFTZ3xYPreHAB+", 1]], [9, "UsernameEditbox", 7, [-87, -88, -89], [-86], [0, "17ujXnFFNEI4HG5agaMkHK", 1], [5, 240, 40], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "PasswordEditbox", 7, [-91, -92, -93], [-90], [0, "862ssh+0FOI4MWhb0ilQ0P", 1], [5, 240, 40], [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Background", 512, [-96], [[1, 1, 0, -94, [10], 11], [4, 0, 45, 100, 40, -95]], [0, "579SLj6gBDsqPFwKbzBGBy", 1], [5, 100, 40]], [9, "autoLoginToggle", 7, [-98, -99, -100], [-97], [0, "43xafN6GhHDopQLHT+5qLb", 1], [5, 28, 28], [164.419, -74, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "scrollBar", 512, 5, [-104], [[-101, [61, 0, 37, 350.07654921020657, 237, -102], [1, 1, 0, -103, [66], 67]], 1, 4, 4], [0, "72rkSqC7ZKWZZWzT+YfSKl", 5], [5, 12, 780], [0, 1, 0.5], [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 512, 2, [-107], [[1, 1, 0, -105, [23], 24], [4, 0, 45, 100, 40, -106]], [0, "325xvnnZdIzL9nKEDKkUqA", 2], [5, 250, 140]], [8, "Background", 512, 3, [-110], [[1, 1, 0, -108, [37], 38], [4, 0, 45, 100, 40, -109]], [0, "325xvnnZdIzL9nKEDKkUqA", 3], [5, 250, 140]], [8, "Background", 512, 4, [-113], [[1, 1, 0, -111, [51], 52], [4, 0, 45, 100, 40, -112]], [0, "325xvnnZdIzL9nKEDKkUqA", 4], [5, 250, 140]], [14, "Background", 512, [-116], [[1, 1, 0, -114, [71], 72], [4, 0, 45, 100, 40, -115]], [0, "a83UFg49pKDI2IQT70utbU", 1], [5, 100, 40]], [6, "ScrollView", [-120], [[[1, 1, 0, -117, [79], 80], -118, [34, 45, -145.72599999999997, -134.27400000000003, 5.684341886080802e-14, 180, 240, 250, -119]], 4, 1, 4], [0, "37MAvaLv5Jj4mT2dCoFARj", 11], [5, 580, 900], [-5.725999999999999, 90, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Background", 512, [-123], [[1, 1, 0, -121, [82], 83], [4, 0, 45, 100, 40, -122]], [0, "cd90IcfjRETpUEph0p7Cnb", 1], [5, 1920, 1080]], [40, "<PERSON><PERSON>", "a5esZu+45LA5mBpvttspPD", [-126], [[59, true, -124, [5, 1920, 1080]], [33, 45, -125]], [5, 1920, 1080], [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Lobby", 1, [13, 6, -127], [0, "edduYMEoxEz7IVP9PQmywe", 1]], [48, "Background", 6, [[1, 1, 0, -129, [18], 19], [20, 45, 40, 36, -130]], [0, "b9dd7OqiNKdqdco2x7+B2y", -128], [4, 4284769380], [5, 600, 900]], [49, "content", 512, [12], [[20, 45, 220, 400, -131]], [0, "4cBfOJ6lJDdrYiZ8HvCnKb", 5], [4, 4286611584], [5, 568, 780], [0, 0.5, 1], [0, 390, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "view", 512, 5, [28], [[36, 0, -132, [21]], [60, 45, 12, 240, 250, -133]], [0, "82P0+WowZHZJNje0dxboQy", 5], [5, 568, 780], [-6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "DownloadIcons", 2, [-134, -135, -136], [0, "31ml3l7SNIN7U5kk70tTmC", 2], [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "panel", 8, [-137, -138], [0, "adIYsnvxNGdJZZ05q4J1uY", 8]], [19, "ProgressBar", 31, [-141], [[-139, [23, 0, -140, [30], 31]], 1, 4], [0, "6aKAke7rtM/7vkkg0WFx/q", 8], [5, 221, 20], [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "DownloadIcons", 3, [-142, -143, -144], [0, "31ml3l7SNIN7U5kk70tTmC", 3], [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "panel", 9, [-145, -146], [0, "adIYsnvxNGdJZZ05q4J1uY", 9]], [19, "ProgressBar", 34, [-149], [[-147, [23, 0, -148, [44], 45]], 1, 4], [0, "6aKAke7rtM/7vkkg0WFx/q", 9], [5, 221, 20], [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "DownloadIcons", 4, [-150, -151, -152], [0, "31ml3l7SNIN7U5kk70tTmC", 4], [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "panel", 10, [-153, -154], [0, "adIYsnvxNGdJZZ05q4J1uY", 10]], [19, "ProgressBar", 37, [-157], [[-155, [23, 0, -156, [58], 59]], 1, 4], [0, "6aKAke7rtM/7vkkg0WFx/q", 10], [5, 221, 20], [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "RoomListPanel", 11, [23], [[77, 2, -158, [5, 300, 1080]], [34, 5, 810, 810, 11.460000000000047, -11.460000000000047, 300, 200, -159]], [0, "4clgvRfx1IhaHw3pFTmAni", 11], [5, 300, 1080], [0, -11.460000000000036, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "New Node", false, [25, 1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "BACKGROUND_SPRITE", 512, 14, [[-160, [4, 0, 45, 160, 40, -161]], 1, 4], [0, "bbs4HzEoJByK4CGML//tmL", 1], [5, 240, 40]], [31, "TEXT_LABEL", 512, 14, [[-162, [12, 0, 45, 2, 158, 40, -163]], 1, 4], [0, "894k0bUm5GhJZgmshAcDMb", 1], [5, 238, 40], [0, 0, 1], [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "PLACEHOLDER_LABEL", 512, false, 14, [[-164, [12, 0, 45, 2, 158, 40, -165]], 1, 4], [0, "a2nRQzfCpHQYCfEPUtdxQO", 1], [4, 4290493371], [5, 238, 40], [0, 0, 1], [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "BACKGROUND_SPRITE", 512, 15, [[-166, [4, 0, 45, 160, 40, -167]], 1, 4], [0, "b9GV21Zw9C+qP00RIPjDVP", 1], [5, 240, 40]], [31, "TEXT_LABEL", 512, 15, [[-168, [12, 0, 45, 2, 158, 40, -169]], 1, 4], [0, "76qnZcPH9J2rfLoUkBmI0q", 1], [5, 238, 40], [0, 0, 1], [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "PLACEHOLDER_LABEL", 512, false, 15, [[-170, [12, 0, 45, 2, 158, 40, -171]], 1, 4], [0, "55p/v2O8VNBo+lVPBhC2q4", 1], [4, 4290493371], [5, 238, 40], [0, 0, 1], [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "LoginButton", 7, [16], [-172], [0, "5dsaakQQVDBrExilWCHkFm", 1], [5, 100, 40], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "Background", 512, 17, [[65, -173, [12], 13]], [0, "0dby/y/7lL7IaG0v+/cMS1", 1], [5, 28, 28], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [29, "LanguageSettingButton", 6, [22], [[71, 2, -175, [[35, "244f9nlJENGzI4gsBJoi3Ke", "show", -174]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 22, 74, 75, 76, 77]], [0, "655gHORS5PbZFW8pxV0lGl", 1], [5, 100, 40], [222.095, 413.962, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "LanguageSetting", false, 26, [63, "27CZnL139DdZaO5JdeT4VQ", true, -176, 73]], [8, "view", 512, 23, [-178], [[36, 0, -177, [78]]], [0, "d1zTjQQfROT7mkp8JSekXt", 11], [5, 360, 500]], [53, "content", 512, 51, [[76, 1, 2, 20, 30, -179, [5, 180, 216]]], [0, "98Mdwzj5xPmZV09zws4eBr", 11], [5, 180, 216], [0, 0.5, 1], [0, 115.30999755859375, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "ShieldButton", false, 0, 6, [24], [[72, -180, [4, 4293322470], [4, 4291348680], [4, 3363338360], 24, 84, 85, 86, 87]], [0, "aa7hQeXkVI06vQ/efI+7Ws", 1], [5, 1920, 1080]], [41, "Main Camera", 25, [[58, 7, -1, -181]], [5, 960, 640]], [43, "Update", 1, [[62, -182, 0, 1, 2]], [0, "cdbhFoDblHJIqzjDitul8f", 1]], [21, 1, 0, 41, [3]], [13, "a5team@0050", 20, 25, false, 1, 1, 42, [4]], [13, "Enter username here...", 20, 25, false, 1, 1, 43, [5]], [68, "a5team@0050", 256, 6, 14, 57, 58, 56], [21, 1, 0, 44, [6]], [13, "●●●●●●", 20, 25, false, 1, 1, 45, [7]], [13, "Enter password here...", 20, 25, false, 1, 1, 46, [8]], [69, "654321", 256, 0, 6, 15, 61, 62, 60], [15, "Label", 512, 16, [[5, "<PERSON><PERSON>", 20, false, 1, 1, 1, 1, -183, [9]]], [0, "cc3ufLAzdN+5frAXfHvA/2", 1], [4, 4278190080], [5, 100, 40]], [70, 2, 47, [[35, "aa5461LSq5Mr6EBXJc0/jvc", "login", 13]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 16], [55, "checkmark", 512, 17, [-184], [0, "b1u8T0tGdNFIdijh+G1IBC", 1], [5, 28, 28]], [66, 2, false, 66, [14]], [10, "autoLoginLabel", 17, [-185], [0, "f2QH5Y+LpNEbNvH0YQh+Vm", 1], [5, 100, 50.4], [-66.578, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [67, "自动登录", 25, 1, 1, 68, [15]], [73, 3, 17, [4, 4292269782], 48, 67], [11, "bar", 512, 18, [-186], [0, "17i3b9OUpNt4H4QM3ZDVep", 5], [5, 10, 156.25], [0, 0, 0], [-11, -31.25, 0, 0, 0, 0, 1, 1, 1, 1]], [21, 1, 0, 71, [20]], [79, 1, 18, 72], [80, false, 0.75, 0.23, null, 5, 28, 73], [17, "Label", 512, 19, [-187], [0, "6cy8tJmYpM1JzlIJwywTA5", 2], [4, 4278190080], [5, 230, 40]], [5, "Cowboy", 30, false, 1, 1, 1, 1, 75, [22]], [2, "cloud-download", false, 30, [-188], [0, "fef9N/oBVAFrdNtKrqLZbq", 2], [5, 64, 60]], [3, 77, [25]], [2, "cloud-download-fail", false, 30, [-189], [0, "eaoXhSeMhLBZ2gunzdLkov", 2], [5, 54, 37]], [3, 79, [26]], [2, "cloud-disconnected", false, 30, [-190], [0, "a0aYrLhM1PN63JYbRX1G5s", 2], [5, 64, 56]], [3, 81, [27]], [10, "barText", 31, [-191], [0, "eesvcEnh1BRYEsC64V3sZp", 8], [5, 0, 37.8], [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 28, 30, 1, 1, 83, [28]], [11, "bar", 512, 32, [-192], [0, "01Jrx0A3dGm7qhs0zzxMPI", 8], [5, 0, 20], [0, 0, 0.5], [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 0, 85, [29]], [26, 0, 0, 32, 86], [27, 8, 87, 84, 31], [28, 2, 88], [17, "Label", 512, 20, [-193], [0, "6cy8tJmYpM1JzlIJwywTA5", 3], [4, 4278190080], [5, 230, 40]], [5, "Cowboy", 30, false, 1, 1, 1, 1, 90, [36]], [2, "cloud-download", false, 33, [-194], [0, "fef9N/oBVAFrdNtKrqLZbq", 3], [5, 64, 60]], [3, 92, [39]], [2, "cloud-download-fail", false, 33, [-195], [0, "eaoXhSeMhLBZ2gunzdLkov", 3], [5, 54, 37]], [3, 94, [40]], [2, "cloud-disconnected", false, 33, [-196], [0, "a0aYrLhM1PN63JYbRX1G5s", 3], [5, 64, 56]], [3, 96, [41]], [10, "barText", 34, [-197], [0, "eesvcEnh1BRYEsC64V3sZp", 9], [5, 0, 37.8], [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 28, 30, 1, 1, 98, [42]], [11, "bar", 512, 35, [-198], [0, "01Jrx0A3dGm7qhs0zzxMPI", 9], [5, 0, 20], [0, 0, 0.5], [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 0, 100, [43]], [26, 0, 0, 35, 101], [27, 9, 102, 99, 34], [28, 3, 103], [17, "Label", 512, 21, [-199], [0, "6cy8tJmYpM1JzlIJwywTA5", 4], [4, 4278190080], [5, 230, 40]], [5, "Cowboy", 30, false, 1, 1, 1, 1, 105, [50]], [2, "cloud-download", false, 36, [-200], [0, "fef9N/oBVAFrdNtKrqLZbq", 4], [5, 64, 60]], [3, 107, [53]], [2, "cloud-download-fail", false, 36, [-201], [0, "eaoXhSeMhLBZ2gunzdLkov", 4], [5, 54, 37]], [3, 109, [54]], [2, "cloud-disconnected", false, 36, [-202], [0, "a0aYrLhM1PN63JYbRX1G5s", 4], [5, 64, 56]], [3, 111, [55]], [10, "barText", 37, [-203], [0, "eesvcEnh1BRYEsC64V3sZp", 10], [5, 0, 37.8], [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 28, 30, 1, 1, 113, [56]], [11, "bar", 512, 38, [-204], [0, "01Jrx0A3dGm7qhs0zzxMPI", 10], [5, 0, 20], [0, 0, 0.5], [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 0, 115, [57]], [26, 0, 0, 38, 116], [27, 10, 117, 114, 37], [28, 4, 118], [15, "Label", 512, 22, [[5, "語言", 20, false, 1, 1, 1, 1, -205, [70]]], [0, "87++rKoV9KkJe20Nl38SlN", 1], [4, 4278190080], [5, 100, 40]], [81, false, 0.75, 0.23, null, null, 23, 52], [83, 11, 121], [15, "Label", 512, 24, [[5, "button", 20, false, 1, 1, 1, 1, -206, [81]]], [0, "22P7mP6RVLMpHJJ/vn/GM4", 1], [4, 4278190080], [5, 100, 40]]], 0, [0, 3, 1, 0, 0, 1, 0, 19, 6, 0, 20, 13, 0, 0, 1, 0, -1, 55, 0, -2, 26, 0, 3, 2, 0, 8, 19, 0, 0, 2, 0, 9, 89, 0, 10, 82, 0, 11, 80, 0, 12, 78, 0, 13, 76, 0, 0, 2, 0, -3, 89, 0, -1, 19, 0, -2, 30, 0, -3, 8, 0, 3, 3, 0, 8, 20, 0, 0, 3, 0, 9, 104, 0, 10, 97, 0, 11, 95, 0, 12, 93, 0, 13, 91, 0, 0, 3, 0, -3, 104, 0, -1, 20, 0, -2, 33, 0, -3, 9, 0, 3, 4, 0, 8, 21, 0, 0, 4, 0, 9, 119, 0, 10, 112, 0, 11, 110, 0, 12, 108, 0, 13, 106, 0, 0, 4, 0, -3, 119, 0, -1, 21, 0, -2, 36, 0, -3, 10, 0, 3, 5, 0, 0, 5, 0, -2, 74, 0, -1, 18, 0, -2, 29, 0, 21, 122, 0, 0, 6, 0, -1, 27, 0, -3, 49, 0, -4, 11, 0, -5, 53, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, -2, 15, 0, -3, 47, 0, -4, 17, 0, 3, 8, 0, -1, 88, 0, -1, 31, 0, 3, 9, 0, -1, 103, 0, -1, 34, 0, 3, 10, 0, -1, 118, 0, -1, 37, 0, 3, 11, 0, -1, 122, 0, -1, 39, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 22, 69, 0, 23, 70, 0, 24, 65, 0, 25, 63, 0, 26, 59, 0, 0, 13, 0, -1, 59, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, -1, 63, 0, -1, 44, 0, -2, 45, 0, -3, 46, 0, 0, 16, 0, 0, 16, 0, -1, 64, 0, -1, 70, 0, -1, 48, 0, -2, 66, 0, -3, 68, 0, -1, 73, 0, 0, 18, 0, 0, 18, 0, -1, 71, 0, 0, 19, 0, 0, 19, 0, -1, 75, 0, 0, 20, 0, 0, 20, 0, -1, 90, 0, 0, 21, 0, 0, 21, 0, -1, 105, 0, 0, 22, 0, 0, 22, 0, -1, 120, 0, 0, 23, 0, -2, 121, 0, 0, 23, 0, -1, 51, 0, 0, 24, 0, 0, 24, 0, -1, 123, 0, 0, 25, 0, 0, 25, 0, -1, 54, 0, -3, 50, 0, 3, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, -1, 77, 0, -2, 79, 0, -3, 81, 0, -1, 83, 0, -2, 32, 0, -1, 87, 0, 0, 32, 0, -1, 85, 0, -1, 92, 0, -2, 94, 0, -3, 96, 0, -1, 98, 0, -2, 35, 0, -1, 102, 0, 0, 35, 0, -1, 100, 0, -1, 107, 0, -2, 109, 0, -3, 111, 0, -1, 113, 0, -2, 38, 0, -1, 117, 0, 0, 38, 0, -1, 115, 0, 0, 39, 0, 0, 39, 0, -1, 56, 0, 0, 41, 0, -1, 57, 0, 0, 42, 0, -1, 58, 0, 0, 43, 0, -1, 60, 0, 0, 44, 0, -1, 61, 0, 0, 45, 0, -1, 62, 0, 0, 46, 0, -1, 65, 0, 0, 48, 0, 27, 50, 0, 0, 49, 0, 3, 50, 0, 0, 51, 0, -1, 52, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 64, 0, -1, 67, 0, -1, 69, 0, -1, 72, 0, -1, 76, 0, -1, 78, 0, -1, 80, 0, -1, 82, 0, -1, 84, 0, -1, 86, 0, -1, 91, 0, -1, 93, 0, -1, 95, 0, -1, 97, 0, -1, 99, 0, -1, 101, 0, -1, 106, 0, -1, 108, 0, -1, 110, 0, -1, 112, 0, -1, 114, 0, -1, 116, 0, 0, 120, 0, 0, 123, 0, 28, 40, 1, 2, 40, 2, 2, 12, 3, 2, 12, 4, 2, 12, 5, 2, 6, 6, 2, 26, 7, 2, 13, 12, 2, 28, 13, 2, 26, 16, 2, 47, 22, 2, 49, 23, 2, 39, 24, 2, 53, 25, 2, 40, 28, 2, 29, 73, 29, 74, 206], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 60, 65, 65, 65, 65, 67, 72, 78, 80, 82, 86, 93, 95, 97, 101, 108, 110, 112, 116, 122], [14, 15, 16, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, -1, -1, 1, 4, 5, 6, 7, -1, -1, 1, -1, -1, -1, -1, -1, -1, 1, 4, 5, 6, 7, -1, -1, 1, -1, -1, -1, -1, -1, -1, 1, 4, 5, 6, 7, -1, 1, -1, 1, -1, 1, -1, -1, 1, 17, 4, 5, 6, 7, -1, -1, 1, -1, -1, 1, 4, 5, 6, 7, 1, 1, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18], [11, 12, 13, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 14, 0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 4, 1, 2, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 4, 1, 2, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 4, 1, 2, 1, 1, 0, 3, 0, 15, 0, 3, 0, 0, 1, 16, 1, 2, 1, 5, 0, 0, 3, 0, 0, 1, 1, 2, 1, 5, 10, 10, 1, 2, 1, 5, 17, 18, 6, 7, 8, 9, 6, 7, 8, 9, 6, 7, 8, 9, 19]]