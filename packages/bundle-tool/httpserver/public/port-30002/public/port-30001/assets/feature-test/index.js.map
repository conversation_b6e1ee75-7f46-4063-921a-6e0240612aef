{"version": 3, "sources": ["assets/bundles/feature-test/scripts/feature-test-entry.ts", "assets/bundles/feature-test/scripts/common/feature-test-macros.ts", "assets/bundles/feature-test/scripts/components/feature-test-top-menu.ts", "assets/bundles/feature-test/scripts/components/test-luck-turntable.ts"], "names": ["pf_1", "require", "pf", "feature_test_macros_1", "BUNDLE_NAME", "macros", "FeatureTestEntry", "_super", "__extends", "_this", "call", "this", "bundleType", "BUNDLE_TYPE", "BUNDLE_RESOURCE", "prototype", "getLanguageStringPath", "getAddressableConfigPath", "languageManager", "currentLanguage", "LANGUAGE_GROUPS", "zh_CN", "Addressable_Config_Path", "ZH_CN", "EN_US", "onLoad", "__awaiter", "Promise", "cc", "log", "onEnter", "options", "loadConfigs", "_a", "sent", "assetLoader", "AddressalbeAssetLoader", "addLoadAddressableGroupTask", "start", "onProgress", "prefab", "addressableAssetManager", "getAsset", "_featureMenu", "instantiate", "game", "addPersistRootNode", "resolve", "onExit", "removeFromParent", "destroy", "bundleManager", "releaseAll", "bundle", "onUnload", "__decorate", "registerEntry", "BundleEntryBase", "exports", "Assets", "Feature_Order", "_decorator", "ccclass", "property", "FeatureTestTopMenu", "apply", "arguments", "listNode", "_featureNodeMap", "Map", "node", "TEST_LUCK_TURNTABLE", "set", "<PERSON><PERSON><PERSON><PERSON>", "active", "setPosition", "Vec2", "ZERO", "setSiblingIndex", "LUCK_TURNTABLE", "toggleList", "toggleEventHandler", "toggle", "customEventData", "console", "isChecked", "has", "get", "warn", "Node", "Component", "default", "TestLuckTurntable", "awardType", "currencyType", "prizeIndex", "_luckTurntableService", "serviceManager", "services", "LuckTurntableService", "testStartTime", "testEndTime", "testReady", "testCountdown", "testOver", "testDraw", "Number", "string", "testResult", "EditBox"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,IAAAC,EAAA,wCACAC,IAAAD,EAAA,wCACAE,IAAAF,EAAA,iCAEAG,IAAAD,EAAAE,OAAAD,aAGAE,IAAA,SAAAC;AAAsCC,EAAAF,GAAAC;AAGlC,SAAAD;AAAA,IAAAG,IAAAF,EAAAG,KAAAC,SAAAA;AAEIF,EAAAG,aAAAV,EAAAW,YAAAC;;;AAGMR,EAAAS,UAAAC,wBAAA;AACN,OAAA;;AAGMV,EAAAS,UAAAE,2BAAA;AACN,OAAAf,EAAAgB,gBAAAC,oBAAAjB,EAAAkB,gBAAAC,QACIlB,EAAAE,OAAAiB,wBAAAC,QACApB,EAAAE,OAAAiB,wBAAAE;;AAGFlB,EAAAS,UAAAU,SAAA;AAAqC,OAAAC,EAAAf,WAAA,GAAAgB,SAAA;;AACvCC,GAAAC,IAAA,YAAAzB,IAAA;;;;;AAGEE,EAAAS,UAAAe,UAAA,SAAAC;AAAsC,OAAAL,EAAAf,WAAA,GAAAgB,SAAA;;;;;AACxCC,GAAAC,IAAA,YAAAzB,IAAA;AAEA,OAAA,EAAA,GAAAO,KAAAqB;;;AAAAC,EAAAC;CAEMC,IAAA,IAAAjC,EAAAkC,0BACNC,4BAAA;AACA,OAAA,EAAA,GAAAF,EAAAG,MAAAP,QAAAA,SAAA,IAAAA,EAAAQ;;;AAAAN,EAAAC;AAEMM,IAAAtC,EAAAuC,wBAAAC,SAAA;AACN/B,KAAAgC,eAAAf,GAAAgB,YAAAJ;AACAZ,GAAAiB,KAAAC,mBAAAnC,KAAAgC;AAEA,OAAA,EAAA,GAAAhB,QAAAoB;;;;;AAGEzC,EAAAS,UAAAiC,SAAA;AAAU,OAAAtB,EAAAf,WAAA,GAAAgB,SAAA;;AACZC,GAAAC,IAAA,YAAAzB,IAAA;AACAO,KAAAgC,aAAAM;AACAtC,KAAAgC,aAAAO;AACAvC,KAAAgC,eAAA;AACAzC,EAAAiD,cAAAC,WAAAzC,KAAA0C;;;;;AAGJ/C,EAAAS,UAAAuC,WAAA;AACI1B,GAAAC,IAAA,YAAAzB,IAAA;;AAER,OAjDamD,EAAA,EADZvD,EAAAwD,cAAApD,MACYE;CAAb,CAAAJ,EAAAuD;AAAaC,EAAApD,mBAAAA;;;;;;;;;;;;;CCPb,SAAAD;AACiBA,EAAAD,cAAA;CAEb,SAAAkB;AACIA,EAAA,QAAA;AACAA,EAAA,QAAA;EAFJ,CAGAjB,EAAAiB,4BAAAjB,EAAAiB,0BAAA;CAIAjB,EAAAsD,WAAAtD,EAAAsD,SAAA,KADI,sBAAA;CAGJ,SAAAC;AACIA,EAAAA,EAAA,iBAAA,KAAA;EADJ,CAEAvD,EAAAuD,kBAAAvD,EAAAuD,gBAAA;EAdJ,CAeAF,EAAArD,WAAAqD,EAAArD,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfA,IAAAH,IAAAD,EAAA,2CACAE,IAAAF,EAAA,kCAEMgC,IAAAL,GAAAiC,YAAAC,IAAA7B,EAAA6B,SAAAC,IAAA9B,EAAA8B,UAGNC,IAAA,SAAAzD;AAAgDC,EAAAwD,GAAAzD;AAAhD,SAAAyD;AAAA,IAAAvD,IAAA,SAAAF,KAAAA,EAAA0D,MAAAtD,MAAAuD,cAAAvD;AAEIF,EAAA0D,WAAA;AAEQ1D,EAAA2D,kBAAA,IAAAC;;;AAMRL,EAAAjD,UAAAuB,QAAA;AACI,IAAAgC,IAAA1C,GAAAgB,YAAA1C,EAAAuC,wBAAAC,SAAAvC,EAAAE,OAAAsD,OAAAY;AACA5D,KAAAyD,gBAAAI,IAAArE,EAAAE,OAAAsD,OAAAY,qBAAAD;AACA3D,KAAAwD,SAAAM,SAAAH;AACAA,EAAAI,UAAA;AACAJ,EAAAK,YAAA/C,GAAAgD,KAAAC;AACAP,EAAAQ,gBAAA,IAAA3E,EAAAE,OAAAuD,cAAAmB,iBAAA;;AAKJf,EAAAjD,UAAAiE,aAAA;AACIrE,KAAAwD,SAAAO,UAAA/D,KAAAwD,SAAAO;;AAGJV,EAAAjD,UAAAkE,qBAAA,SAAAC,GAAAC;AACIC,QAAAvD,IAAAqD,EAAAG;AACAD,QAAAvD,IAAAsD;AASAxE,KAAAyD,gBAAAkB,IAAAH,KACID,EAAAG,YACI1E,KAAAyD,gBAAAmB,IAAAJ,GAAAT,UAAA,IAEA/D,KAAAyD,gBAAAmB,IAAAJ,GAAAT,UAAA,IAGJU,QAAAI,KAAA,qBAAAL;;AAzCR5B,EAAA,EADCQ,EAAAnC,GAAA6D,SACwBzB,EAAAjD,WAAA,iBAAA;AA4C7B,OA9CqBwC,EAAA,EADpBO,KACoBE;CAArB,CAAApC,GAAA8D;AAAqBhC,EAAAiC,UAAA3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACArB,IAAA9D,IAAAD,EAAA,2CAEMgC,IAAAL,GAAAiC,YAAAC,IAAA7B,EAAA6B,SAAAC,IAAA9B,EAAA8B,UAGN6B,IAAA,SAAArF;AAA+CC,EAAAoF,GAAArF;AAA/C,SAAAqF;AAAA,IAAAnF,IAAA,SAAAF,KAAAA,EAAA0D,MAAAtD,MAAAuD,cAAAvD;AAEIF,EAAAoF,YAAA;AAGApF,EAAAqF,eAAA;AAGArF,EAAAsF,aAAA;AAEQtF,EAAAuF,wBAAA;;;AAERJ,EAAA7E,UAAAU,SAAA;AACId,KAAAqF,wBAAA9F,EAAA+F,eAAAV,IAAArF,EAAAgG,SAAAC;;AAGJP,EAAA7E,UAAAuB,QAAA;AAEAsD,EAAA7E,UAAAqF,gBAAA;AACIzF,KAAAqF,sBAAAI;;AAGJR,EAAA7E,UAAAsF,cAAA;AACI1F,KAAAqF,sBAAAK;;AAGJT,EAAA7E,UAAAuF,YAAA;AACI3F,KAAAqF,sBAAAM;;AAGJV,EAAA7E,UAAAwF,gBAAA;AACI5F,KAAAqF,sBAAAO;;AAGJX,EAAA7E,UAAAyF,WAAA;AACI7F,KAAAqF,sBAAAQ;;AAGJZ,EAAA7E,UAAA0F,WAAA;AACI9F,KAAAqF,sBAAAS,SAAAC,OAAA/F,KAAAkF,UAAAc,SAAAD,OAAA/F,KAAAmF,aAAAa,SAAAD,OAAA/F,KAAAoF,WAAAY;;AAOJf,EAAA7E,UAAA6F,aAAA;AACIjG,KAAAqF,sBAAAY;;AA7CJrD,EAAA,EADCQ,EAAAnC,GAAAiF,YAC4BjB,EAAA7E,WAAA,kBAAA;AAG7BwC,EAAA,EADCQ,EAAAnC,GAAAiF,YAC+BjB,EAAA7E,WAAA,qBAAA;AAGhCwC,EAAA,EADCQ,EAAAnC,GAAAiF,YAC6BjB,EAAA7E,WAAA,mBAAA;AAyClC,OAjDqBwC,EAAA,EADpBO,KACoB8B;CAArB,CAAAhE,GAAA8D;AAAqBhC,EAAAiC,UAAAC", "sourcesContent": ["import { registerEntry } from '../../../poker-framework/scripts/pf';\nimport * as pf from '../../../poker-framework/scripts/pf';\nimport { macros } from './common/feature-test-macros';\n\nimport BUNDLE_NAME = macros.BUNDLE_NAME;\n\n@registerEntry(BUNDLE_NAME)\nexport class FeatureTestEntry extends pf.BundleEntryBase {\n    private _featureMenu: cc.Node;\n\n    constructor() {\n        super();\n        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_RESOURCE;\n    }\n\n    protected getLanguageStringPath() {\n        return null;\n    }\n\n    protected getAddressableConfigPath() {\n        return pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN\n            ? macros.Addressable_Config_Path.ZH_CN\n            : macros.Addressable_Config_Path.EN_US;\n    }\n\n    async onLoad(options?: pf.IBundleOptions): Promise<void> {\n        cc.log(`bundle ${BUNDLE_NAME} onLoad`);\n    }\n\n    async onEnter(options?: pf.IBundleOptions): Promise<void> {\n        cc.log(`bundle ${BUNDLE_NAME} onEnter`);\n\n        await this.loadConfigs();\n\n        const assetLoader = new pf.AddressalbeAssetLoader();\n        assetLoader.addLoadAddressableGroupTask('feature-test');\n        await assetLoader.start(options?.onProgress);\n\n        const prefab = pf.addressableAssetManager.getAsset<cc.Prefab>('feature-test.test-menu');\n        this._featureMenu = cc.instantiate(prefab);\n        cc.game.addPersistRootNode(this._featureMenu);\n\n        return Promise.resolve();\n    }\n\n    async onExit(): Promise<void> {\n        cc.log(`bundle ${BUNDLE_NAME} onExit`);\n        this._featureMenu.removeFromParent();\n        this._featureMenu.destroy();\n        this._featureMenu = null;\n        pf.bundleManager.releaseAll(this.bundle);\n    }\n\n    onUnload(): void {\n        cc.log(`bundle ${BUNDLE_NAME} onUnload`);\n    }\n}\n", "export namespace macros {\n    export const BUNDLE_NAME = 'feature-test';\n\n    export enum Addressable_Config_Path {\n        ZH_CN = 'configs/feature-test-addressable-assets-zh_cn',\n        EN_US = 'configs/feature-test-addressable-assets-en_us'\n    }\n\n    export enum Assets {\n        TEST_LUCK_TURNTABLE = 'feature-test.test-luck-turntable'\n    }\n\n    export enum Feature_Order {\n        LUCK_TURNTABLE = 0\n    }\n}\n", "import * as pf from '../../../../poker-framework/scripts/pf';\nimport { macros } from '../common/feature-test-macros';\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class FeatureTestTopMenu extends cc.Component {\n    @property(cc.Node)\n    listNode: cc.Node = null;\n\n    private _featureNodeMap: Map<string, cc.Node> = new Map();\n\n    // LIFE-CYCLE CALLBACKS:\n\n    // onLoad () {}\n\n    start() {\n        const node = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.TEST_LUCK_TURNTABLE));\n        this._featureNodeMap.set(macros.Assets.TEST_LUCK_TURNTABLE, node);\n        this.listNode.addChild(node);\n        node.active = false;\n        node.setPosition(cc.Vec2.ZERO);\n        node.setSiblingIndex(macros.Feature_Order.LUCK_TURNTABLE * 2 + 1);\n    }\n\n    // update (dt) {}\n\n    toggleList() {\n        this.listNode.active = !this.listNode.active;\n    }\n\n    toggleEventHandler(toggle, customEventData) {\n        console.log(toggle.isChecked);\n        console.log(customEventData);\n        // if (customEventData === '147') {\n        //     if (toggle.isChecked) {\n        //         console.log('147 checked');\n        //     } else {\n        //         console.log('147 unchecked');\n        //     }\n        // }\n\n        if (this._featureNodeMap.has(customEventData)) {\n            if (toggle.isChecked) {\n                this._featureNodeMap.get(customEventData).active = true;\n            } else {\n                this._featureNodeMap.get(customEventData).active = false;\n            }\n        } else {\n            console.warn('asset not found:' + customEventData);\n        }\n    }\n}\n", "// Learn TypeScript:\n//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html\nimport * as pf from '../../../../poker-framework/scripts/pf';\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class TestLuckTurntable extends cc.Component {\n    @property(cc.EditBox)\n    awardType: cc.EditBox = null;\n\n    @property(cc.EditBox)\n    currencyType: cc.EditBox = null;\n\n    @property(cc.EditBox)\n    prizeIndex: cc.EditBox = null;\n\n    private _luckTurntableService: pf.services.LuckTurntableService = null;\n\n    onLoad() {\n        this._luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);\n    }\n\n    start() {}\n\n    testStartTime() {\n        this._luckTurntableService.testStartTime();\n    }\n\n    testEndTime() {\n        this._luckTurntableService.testEndTime();\n    }\n\n    testReady() {\n        this._luckTurntableService.testReady();\n    }\n\n    testCountdown() {\n        this._luckTurntableService.testCountdown();\n    }\n\n    testOver() {\n        this._luckTurntableService.testOver();\n    }\n\n    testDraw() {\n        this._luckTurntableService.testDraw(\n            Number(this.awardType.string),\n            Number(this.currencyType.string),\n            Number(this.prizeIndex.string)\n        );\n    }\n\n    testResult() {\n        this._luckTurntableService.testResult();\n    }\n}\n"], "file": "index.js"}