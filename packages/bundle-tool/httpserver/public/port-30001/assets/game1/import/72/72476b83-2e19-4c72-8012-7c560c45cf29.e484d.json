[1, ["9dil7mf2JCubaOcd5MjKMe"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "raptor", "\nraptor.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nback_arm\n  rotate: true\n  xy: 140, 191\n  size: 46, 29\n  orig: 46, 29\n  offset: 0, 0\n  index: -1\nback_bracer\n  rotate: true\n  xy: 167, 317\n  size: 39, 28\n  orig: 39, 28\n  offset: 0, 0\n  index: -1\nback_hand\n  rotate: false\n  xy: 167, 358\n  size: 36, 34\n  orig: 36, 34\n  offset: 0, 0\n  index: -1\nback_knee\n  rotate: false\n  xy: 299, 478\n  size: 49, 67\n  orig: 49, 67\n  offset: 0, 0\n  index: -1\nback_thigh\n  rotate: true\n  xy: 167, 437\n  size: 39, 24\n  orig: 39, 24\n  offset: 0, 0\n  index: -1\neyes_closed\n  rotate: true\n  xy: 2, 2\n  size: 47, 45\n  orig: 47, 45\n  offset: 0, 0\n  index: -1\neyes_open\n  rotate: true\n  xy: 49, 2\n  size: 47, 45\n  orig: 47, 45\n  offset: 0, 0\n  index: -1\neyes_surprised\n  rotate: true\n  xy: 96, 2\n  size: 47, 45\n  orig: 47, 45\n  offset: 0, 0\n  index: -1\nfront_arm\n  rotate: false\n  xy: 419, 544\n  size: 48, 30\n  orig: 48, 30\n  offset: 0, 0\n  index: -1\nfront_bracer\n  rotate: false\n  xy: 880, 695\n  size: 41, 29\n  orig: 41, 29\n  offset: 0, 0\n  index: -1\nfront_hand\n  rotate: true\n  xy: 167, 394\n  size: 41, 38\n  orig: 41, 38\n  offset: 0, 0\n  index: -1\nfront_open_hand\n  rotate: false\n  xy: 880, 726\n  size: 43, 44\n  orig: 43, 44\n  offset: 0, 0\n  index: -1\nfront_thigh\n  rotate: false\n  xy: 360, 545\n  size: 57, 29\n  orig: 57, 29\n  offset: 0, 0\n  index: -1\ngun\n  rotate: false\n  xy: 785, 774\n  size: 107, 103\n  orig: 107, 103\n  offset: 0, 0\n  index: -1\ngun_nohand\n  rotate: false\n  xy: 614, 703\n  size: 105, 102\n  orig: 105, 102\n  offset: 0, 0\n  index: -1\nhead\n  rotate: false\n  xy: 2, 137\n  size: 136, 149\n  orig: 136, 149\n  offset: 0, 0\n  index: -1\nlower_leg\n  rotate: true\n  xy: 780, 699\n  size: 73, 98\n  orig: 73, 98\n  offset: 0, 0\n  index: -1\nmouth_grind\n  rotate: false\n  xy: 469, 544\n  size: 47, 30\n  orig: 47, 30\n  offset: 0, 0\n  index: -1\nmouth_oooo\n  rotate: true\n  xy: 894, 772\n  size: 105, 30\n  orig: 105, 30\n  offset: 0, 0\n  index: -1\nmouth_smile\n  rotate: true\n  xy: 140, 239\n  size: 47, 30\n  orig: 47, 30\n  offset: 0, 0\n  index: -1\nneck\n  rotate: true\n  xy: 538, 577\n  size: 18, 21\n  orig: 18, 21\n  offset: 0, 0\n  index: -1\nraptor_arm_back\n  rotate: false\n  xy: 940, 936\n  size: 82, 86\n  orig: 82, 86\n  offset: 0, 0\n  index: -1\nraptor_body\n  rotate: false\n  xy: 2, 737\n  size: 610, 285\n  orig: 610, 285\n  offset: 0, 0\n  index: -1\nraptor_front_arm\n  rotate: true\n  xy: 195, 464\n  size: 81, 102\n  orig: 81, 102\n  offset: 0, 0\n  index: -1\nraptor_front_leg\n  rotate: false\n  xy: 2, 478\n  size: 191, 257\n  orig: 191, 257\n  offset: 0, 0\n  index: -1\nraptor_hindleg_back\n  rotate: false\n  xy: 614, 807\n  size: 169, 215\n  orig: 169, 215\n  offset: 0, 0\n  index: -1\nraptor_horn\n  rotate: false\n  xy: 360, 655\n  size: 182, 80\n  orig: 182, 80\n  offset: 0, 0\n  index: -1\nraptor_horn_back\n  rotate: false\n  xy: 360, 576\n  size: 176, 77\n  orig: 176, 77\n  offset: 0, 0\n  index: -1\nraptor_jaw\n  rotate: false\n  xy: 785, 879\n  size: 153, 143\n  orig: 153, 143\n  offset: 0, 0\n  index: -1\nraptor_saddle_noshadow\n  rotate: false\n  xy: 2, 288\n  size: 163, 188\n  orig: 163, 188\n  offset: 0, 0\n  index: -1\nraptor_saddle_strap_front\n  rotate: false\n  xy: 721, 710\n  size: 57, 95\n  orig: 57, 95\n  offset: 0, 0\n  index: -1\nraptor_saddle_strap_rear\n  rotate: true\n  xy: 940, 880\n  size: 54, 74\n  orig: 54, 74\n  offset: 0, 0\n  index: -1\nraptor_saddle_w_shadow\n  rotate: false\n  xy: 195, 547\n  size: 163, 188\n  orig: 163, 188\n  offset: 0, 0\n  index: -1\nraptor_tongue\n  rotate: true\n  xy: 544, 649\n  size: 86, 64\n  orig: 86, 64\n  offset: 0, 0\n  index: -1\nstirrup_back\n  rotate: true\n  xy: 140, 145\n  size: 44, 35\n  orig: 44, 35\n  offset: 0, 0\n  index: -1\nstirrup_front\n  rotate: false\n  xy: 538, 597\n  size: 45, 50\n  orig: 45, 50\n  offset: 0, 0\n  index: -1\nstirrup_strap\n  rotate: false\n  xy: 350, 497\n  size: 49, 46\n  orig: 49, 46\n  offset: 0, 0\n  index: -1\ntorso\n  rotate: true\n  xy: 610, 647\n  size: 54, 91\n  orig: 54, 91\n  offset: 0, 0\n  index: -1\nvisor\n  rotate: false\n  xy: 2, 51\n  size: 131, 84\n  orig: 131, 84\n  offset: 0, 0\n  index: -1\n", ["raptor.png"], {"skeleton": {"hash": "NYDebVH+Ht9WSskWgX4h79z2c4w", "spine": "3.5.03-beta", "width": 1223.72, "height": 1055.62, "fps": 30, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "hip", "parent": "root", "rotation": 3.15, "x": -136.78, "y": 415.47, "color": "fbff00ff"}, {"name": "torso1", "parent": "hip", "length": 126.25, "rotation": -4.97, "x": 30.03, "y": -0.4, "color": "eaff00ff"}, {"name": "saddle", "parent": "torso1", "length": 50.91, "rotation": 91.8, "x": 4.56, "y": 71.86, "color": "ff7300ff"}, {"name": "spineboy_hip", "parent": "saddle", "length": 0.52, "rotation": 90.01, "x": 81.88, "y": 2.68, "color": "ffffffff"}, {"name": "spineboy_torso", "parent": "spineboy_hip", "length": 122.45, "rotation": -75.85, "x": 1.05, "y": -2.1, "color": "ffffffff"}, {"name": "torso2", "parent": "torso1", "length": 121.2, "rotation": 39.84, "x": 126.25, "y": -0.37, "color": "eaff00ff"}, {"name": "neck", "parent": "torso2", "length": 70.59, "rotation": 41.37, "x": 121.19, "y": 0.34, "color": "eaff00ff"}, {"name": "head", "parent": "neck", "length": 105.5, "rotation": 9.82, "x": 70.59, "y": 0.03, "color": "eaff00ff"}, {"name": "horn_rear", "parent": "head", "length": 73.78, "rotation": 44.31, "x": 99.27, "y": -226.79, "color": "e07800ff"}, {"name": "rear_arm_target", "parent": "horn_rear", "rotation": -133.55, "x": 232.68, "y": 245.84, "color": "e07800ff"}, {"name": "back_arm", "parent": "spineboy_torso", "length": 67.21, "rotation": -120.89, "x": 96.33, "y": -38.46, "color": "ffffffff"}, {"name": "back_bracer", "parent": "back_arm", "length": 43.68, "rotation": 17.48, "x": 67.21, "y": -0.31, "color": "ffffffff"}, {"name": "back_hand", "parent": "back_bracer", "length": 41.97, "rotation": 9.2, "x": 43.68, "y": 0.06, "transform": "noRotationOrReflection", "color": "ffffffff"}, {"name": "spineboy_rear_arm_goal", "parent": "saddle", "x": -30.43, "y": -100.08, "color": "ff0001ff"}, {"name": "back_thigh", "parent": "spineboy_hip", "length": 71.15, "rotation": 160.75, "x": -9.57, "y": 2.31, "color": "ffffffff"}, {"name": "back_knee", "parent": "back_thigh", "length": 97.17, "rotation": -54.97, "x": 71.15, "y": -0.28, "color": "ffffffff"}, {"name": "horn_front", "parent": "head", "length": 87.48, "rotation": 49.36, "x": 82.09, "y": -221.36, "color": "15ff00ff"}, {"name": "front_arm_target", "parent": "horn_front", "rotation": -138.59, "x": 294.58, "y": 234.17, "color": "15ff00ff"}, {"name": "front_arm", "parent": "spineboy_torso", "length": 74.51, "rotation": -118.16, "x": 101.37, "y": 9.78, "color": "ffffffff"}, {"name": "front_bracer", "parent": "front_arm", "length": 39.85, "rotation": 20.3, "x": 74.52, "y": -0.41, "color": "ffffffff"}, {"name": "front_arm1", "parent": "torso2", "length": 109.99, "rotation": 224.54, "x": 46.37, "y": -84.61, "color": "15ff00ff"}, {"name": "front_arm2", "parent": "front_arm1", "length": 86.33, "rotation": 105.23, "x": 109.99, "y": 0.2, "color": "15ff00ff"}, {"name": "front_foot_goal", "parent": "root", "rotation": -0.94, "x": -45.79, "y": -28.67, "color": "ff0000ff"}, {"name": "front_leg_goal", "parent": "front_foot_goal", "x": -106.06, "y": 115.58, "color": "ff0000ff"}, {"name": "front_leg1", "parent": "hip", "length": 251.74, "rotation": -51.5, "x": 27.36, "y": -28.27, "color": "15ff00ff"}, {"name": "front_leg2", "parent": "front_leg1", "length": 208.54, "rotation": 261.93, "x": 251.03, "y": 0.16, "color": "15ff00ff"}, {"name": "front_leg3", "parent": "front_leg2", "length": 118.18, "rotation": 85.46, "x": 208.5, "y": -1.63, "color": "15ff00ff"}, {"name": "front_foot1", "parent": "front_leg3", "length": 57.79, "rotation": 54.46, "x": 118.19, "y": -0.79, "scaleX": 1.126, "color": "15ff00ff"}, {"name": "front_foot2", "parent": "front_foot1", "length": 56.19, "rotation": -2.15, "x": 57.78, "y": -0.02, "scaleX": 0.73, "scaleY": 0.823, "transform": "noRotationOrReflection", "color": "15ff00ff"}, {"name": "front_foot3", "parent": "front_foot2", "length": 129.88, "rotation": -2.7, "x": 49.71, "y": 20.65, "scaleX": 1.154, "color": "15ff00ff"}, {"name": "front_hand", "parent": "front_arm2", "length": 47.55, "rotation": -56.83, "x": 86.33, "y": 0.06, "color": "15ff00ff"}, {"name": "front_hand2", "parent": "front_bracer", "length": 58.18, "rotation": 13.9, "x": 39.98, "y": -0.89, "transform": "noRotationOrReflection", "color": "ffffffff"}, {"name": "spineboy_front_arm_goal", "parent": "saddle", "x": -50.7, "y": -96.93, "color": "ff0004ff"}, {"name": "front_thigh", "parent": "spineboy_hip", "length": 77.79, "rotation": 163.34, "x": 15.51, "y": 17.01, "color": "ffffffff"}, {"name": "lower_leg", "parent": "front_thigh", "length": 111.5, "rotation": -49.62, "x": 77.92, "y": -0.1, "color": "ffffffff"}, {"name": "gun", "parent": "spineboy_hip", "length": 181.35, "rotation": 107.11, "x": 16.86, "y": -7.89, "scaleX": 0.816, "scaleY": 0.816, "color": "ffffffff"}, {"name": "neck2", "parent": "spineboy_torso", "length": 32.04, "rotation": -45.22, "x": 113.44, "y": -15.21, "color": "ffffffff"}, {"name": "head2", "parent": "neck2", "length": 249.64, "rotation": 11.65, "x": 23.01, "y": 3.47, "color": "ffffffff"}, {"name": "jaw", "parent": "head", "length": 203.76, "rotation": -140.14, "x": 29.36, "y": -40.15, "color": "ffff00ff"}, {"name": "rear_arm1", "parent": "torso2", "length": 109.56, "rotation": -124.71, "x": 57.05, "y": -95.38, "color": "e07800ff"}, {"name": "rear_arm2", "parent": "rear_arm1", "length": 85.8, "rotation": 123.56, "x": 109.56, "color": "e07800ff"}, {"name": "rear_foot_goal", "parent": "root", "x": 33.43, "y": 30.81, "color": "ff0000ff"}, {"name": "rear_leg_goal", "parent": "rear_foot_goal", "x": -127.51, "y": 75.99, "color": "ff0000ff"}, {"name": "rear_leg1", "parent": "hip", "length": 226.27, "rotation": -54.76, "x": 55.19, "y": -71.25, "color": "e07800ff"}, {"name": "rear_leg2", "parent": "rear_leg1", "length": 172.58, "rotation": -92.25, "x": 226.32, "y": 0.23, "color": "e07800ff"}, {"name": "rear_leg3", "parent": "rear_leg2", "length": 103.05, "rotation": 82.81, "x": 172.31, "y": 2.21, "color": "e07800ff"}, {"name": "rear_foot1", "parent": "rear_leg3", "length": 84.51, "rotation": 75.43, "x": 102.37, "y": -0.02, "color": "e07800ff"}, {"name": "rear_foot2", "parent": "rear_foot1", "length": 102.31, "rotation": -6.13, "x": 84.49, "y": -0.34, "transform": "noRotationOrReflection", "color": "e07800ff"}, {"name": "rear_hand", "parent": "rear_arm2", "length": 45.8, "rotation": -76.28, "x": 85.8, "y": 0.1, "color": "e07800ff"}, {"name": "saddle_strap_front1", "parent": "saddle", "length": 97.27, "rotation": -148.11, "x": -27.36, "y": -73.38, "color": "ff7300ff"}, {"name": "saddle_strap_front2", "parent": "saddle_strap_front1", "length": 102.74, "rotation": -11.13, "x": 97.29, "y": 0.3, "color": "ff7300ff"}, {"name": "saddle_strap_rear1", "parent": "saddle", "length": 38.62, "rotation": 151.13, "x": -33.34, "y": 87.32, "color": "ff7300ff"}, {"name": "saddle_strap_rear2", "parent": "saddle_strap_rear1", "length": 54.36, "x": 38.63, "y": -0.02, "color": "ff7300ff"}, {"name": "saddle_strap_rear3", "parent": "saddle_strap_rear2", "length": 44.04, "rotation": 3.63, "x": 54.86, "y": 0.19, "color": "ff7300ff"}, {"name": "stirrup", "parent": "saddle", "length": 78.17, "rotation": -68.85, "x": -81.94, "y": -103.38, "color": "ff7300ff"}, {"name": "stirrup_strap1", "parent": "saddle", "length": 43.69, "rotation": -135, "x": -20.38, "y": -29.37, "color": "ff7300ff"}, {"name": "stirrup_strap2", "parent": "stirrup_strap1", "length": 51.62, "rotation": 9.38, "x": 43.7, "color": "ff7300ff"}, {"name": "tail1", "parent": "hip", "length": 162.53, "rotation": 162.92, "x": -20.86, "y": 6.87, "color": "eaff00ff"}, {"name": "tail2", "parent": "tail1", "length": 130.02, "rotation": 30.3, "x": 162.53, "y": -0.82, "color": "eaff00ff"}, {"name": "tail3", "parent": "tail2", "length": 141.06, "rotation": 6.88, "x": 130.02, "y": 0.1, "color": "eaff00ff"}, {"name": "tail4", "parent": "tail3", "length": 126.25, "rotation": -18.86, "x": 141.05, "y": 0.64, "color": "eaff00ff"}, {"name": "tail5", "parent": "tail4", "length": 91.06, "rotation": -22.34, "x": 126.25, "y": -0.47, "color": "eaff00ff"}, {"name": "tongue1", "parent": "head", "length": 55.11, "rotation": -129.04, "x": 20.81, "y": -104.75, "color": "ffff00ff"}, {"name": "tongue2", "parent": "tongue1", "length": 44.66, "rotation": 8.93, "x": 55.59, "y": 0.93, "color": "fff200ff"}, {"name": "tongue3", "parent": "tongue2", "length": 43.64, "rotation": 12.86, "x": 44.26, "y": -0.2, "color": "fff200ff"}], "slots": [{"name": "back_hand", "bone": "back_hand", "attachment": "back_hand"}, {"name": "back_arm", "bone": "back_arm", "attachment": "back_arm"}, {"name": "back_bracer", "bone": "back_bracer", "attachment": "back_bracer"}, {"name": "back_knee", "bone": "back_knee", "attachment": "back_knee"}, {"name": "raptor_horn_back", "bone": "horn_rear", "attachment": "raptor_horn_back"}, {"name": "raptor_tongue", "bone": "root", "attachment": "raptor_tongue"}, {"name": "raptor_hindleg_back", "bone": "rear_leg1", "attachment": "raptor_hindleg_back"}, {"name": "raptor_arm_back", "bone": "root", "attachment": "raptor_arm_back"}, {"name": "raptor_body", "bone": "torso1", "attachment": "raptor_body"}, {"name": "back_thigh", "bone": "back_thigh", "attachment": "back_thigh"}, {"name": "raptor_saddle_strap_front", "bone": "saddle_strap_front1", "attachment": "raptor_saddle_strap_front"}, {"name": "raptor_saddle_strap_rear", "bone": "saddle_strap_rear1", "attachment": "raptor_saddle_strap_rear"}, {"name": "raptor_saddle_w_shadow", "bone": "saddle", "attachment": "raptor_saddle_w_shadow"}, {"name": "raptor_saddle_noshadow", "bone": "saddle"}, {"name": "raptor_front_arm", "bone": "root", "attachment": "raptor_front_arm"}, {"name": "raptor_front_leg", "bone": "front_leg1", "attachment": "raptor_front_leg"}, {"name": "raptor_jaw", "bone": "jaw", "attachment": "raptor_jaw"}, {"name": "neck", "bone": "neck2", "attachment": "neck"}, {"name": "spineboy_torso", "bone": "spineboy_torso", "attachment": "torso"}, {"name": "head", "bone": "head2", "attachment": "head"}, {"name": "eyes_open", "bone": "head2", "attachment": "eyes_open"}, {"name": "mouth_smile", "bone": "head2", "attachment": "mouth_smile"}, {"name": "visor", "bone": "head2", "attachment": "visor"}, {"name": "raptor_horn", "bone": "horn_front", "attachment": "raptor_horn"}, {"name": "front_thigh", "bone": "front_thigh", "attachment": "front_thigh"}, {"name": "stirrup_back", "bone": "stirrup", "attachment": "stirrup_back"}, {"name": "lower_leg", "bone": "lower_leg", "attachment": "lower_leg"}, {"name": "stirrup_strap", "bone": "stirrup", "attachment": "stirrup_strap"}, {"name": "stirrup_front", "bone": "stirrup", "attachment": "stirrup_front"}, {"name": "gun", "bone": "gun", "attachment": "gun_nohand"}, {"name": "front_arm", "bone": "front_arm", "attachment": "front_arm"}, {"name": "front_bracer", "bone": "front_bracer", "attachment": "front_bracer"}, {"name": "front_hand", "bone": "front_hand2", "attachment": "front_hand"}], "ik": [{"name": "front_arm_goal", "order": 0, "target": "front_arm_target", "bones": ["front_arm", "front_bracer"]}, {"name": "front_foot_goal", "order": 2, "target": "front_foot_goal", "bones": ["front_leg3", "front_foot1"]}, {"name": "front_leg_goal", "order": 1, "target": "front_leg_goal", "bendPositive": false, "bones": ["front_leg1", "front_leg2"]}, {"name": "rear_arm_goal", "order": 3, "target": "rear_arm_target", "bones": ["back_arm", "back_bracer"]}, {"name": "rear_foot_goal", "order": 5, "target": "rear_foot_goal", "bones": ["rear_leg3", "rear_foot1"]}, {"name": "rear_leg_goal", "order": 4, "target": "rear_leg_goal", "bendPositive": false, "bones": ["rear_leg1", "rear_leg2"]}, {"name": "spineboy_front_leg_goal", "order": 6, "target": "spineboy_front_arm_goal", "bendPositive": false, "bones": ["front_thigh", "lower_leg"]}, {"name": "spineboy_rear_leg_goal", "order": 7, "target": "spineboy_rear_arm_goal", "bendPositive": false, "bones": ["back_thigh", "back_knee"]}, {"name": "stirrup", "order": 8, "target": "stirrup", "bones": ["stirrup_strap1", "stirrup_strap2"]}], "skins": {"default": {"back_arm": {"back_arm": {"x": 29.71, "y": 2.04, "rotation": 16.75, "width": 91, "height": 57}}, "back_bracer": {"back_bracer": {"x": 13.19, "y": -4.28, "rotation": -0.72, "width": 77, "height": 55}}, "back_hand": {"back_hand": {"x": 18.6, "y": 4.23, "rotation": -10.99, "width": 72, "height": 68}}, "back_knee": {"back_knee": {"x": 45.77, "y": 20.47, "rotation": 74.22, "width": 97, "height": 134}}, "back_thigh": {"back_thigh": {"x": 37.85, "y": -4.36, "rotation": 19.24, "width": 78, "height": 47}}, "eyes_open": {"eyes_open": {"x": 93.23, "y": -25.45, "rotation": -70.57, "width": 93, "height": 89}}, "front_arm": {"front_arm": {"x": 31.38, "y": 5.09, "rotation": 14.02, "width": 96, "height": 60}}, "front_bracer": {"front_bracer": {"x": 11.68, "y": -1.36, "rotation": -6.28, "width": 81, "height": 58}}, "front_hand": {"front_hand": {"x": 35.7, "y": 7.84, "rotation": -13.96, "width": 82, "height": 75}, "front_open_hand": {"x": 42.54, "y": 4.62, "rotation": 62.19, "width": 86, "height": 87}, "gun": {"x": 98.9, "y": 22.97, "rotation": 56.34, "width": 213, "height": 206}}, "front_thigh": {"front_thigh": {"x": 45.7, "y": -3.1, "rotation": 16.65, "width": 114, "height": 58}}, "gun": {"gun_nohand": {"type": "mesh", "hull": 13, "width": 210, "height": 203, "uvs": [0.71081, 0.16149, 0.85807, 0.41784, 1, 0.6649, 1, 1, 0.71457, 1, 0.49802, 0.6905, 0.30182, 0.41009, 0, 0.58226, 0, 0.1174, 0.27187, 0.12429, 0.24857, 0, 0.36658, 0, 0.61804, 0, 0.70575, 0.53546, 0.53668, 0.26855], "triangles": [3, 13, 2, 5, 13, 4, 3, 4, 13, 13, 6, 14, 13, 5, 6, 13, 1, 2, 6, 8, 9, 6, 7, 8, 13, 14, 1, 14, 0, 1, 6, 9, 14, 9, 11, 14, 14, 12, 0, 14, 11, 12, 9, 10, 11], "vertices": [23.47999, 50.63, 83.86, 46.31999, 142.05, 42.16999, 197.91, 3.33999, 163.7, -45.86, 86.15, -47.34, 15.89999, -48.68, 8.42, -120.68, -69.05999, -66.80999, -35.31999, -20.72999, -58.83, -10.35, -44.68999, 9.98999, -14.55, 53.34999, 85.20999, 6.42999, 20.45, 8.19999], "edges": [14, 12, 6, 8, 6, 4, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 8, 10, 10, 12, 6, 26, 10, 26, 4, 2, 26, 2, 22, 28, 28, 26, 12, 28, 2, 0, 0, 24, 28, 0, 18, 12]}}, "head": {"head": {"x": 132.33, "y": 1.19, "rotation": -70.57, "width": 271, "height": 298}}, "lower_leg": {"lower_leg": {"x": 76.2, "y": 22.2, "rotation": 66.28, "width": 146, "height": 195}}, "mouth_smile": {"mouth_smile": {"x": 27.66, "y": -31.33, "rotation": -70.57, "width": 93, "height": 59}}, "neck": {"neck": {"x": 15.09, "y": -1.66, "rotation": -58.91, "width": 36, "height": 41}}, "raptor_arm_back": {"raptor_arm_back": {"type": "mesh", "hull": 25, "width": 163, "height": 172, "uvs": [0.38711, 0.29362, 0.31382, 0.46513, 0.29242, 0.51521, 0.32475, 0.4931, 0.57587, 0.32138, 0.63254, 0.28263, 0.71632, 0.34507, 0.94948, 0.51888, 0.94947, 0.60129, 1, 0.65257, 1, 0.90624, 0.95462, 0.99934, 0.88957, 0.83204, 0.80294, 0.99998, 0.75236, 0.75696, 0.6654, 0.713, 0.62288, 0.63242, 0.58194, 0.65031, 0.22478, 0.80641, 0.07791, 0.73315, 0.07825, 0.66549, 0.07984, 0.34306, 0, 0.29728, 0, 0, 0.32334, 0], "triangles": [6, 15, 16, 5, 6, 16, 5, 16, 4, 6, 7, 15, 16, 17, 4, 8, 15, 7, 14, 15, 8, 12, 14, 8, 12, 8, 9, 12, 9, 10, 11, 12, 10, 13, 14, 12, 17, 3, 4, 19, 20, 2, 18, 19, 2, 18, 2, 3, 18, 3, 17, 21, 22, 23, 24, 21, 23, 0, 21, 24, 1, 21, 0, 1, 20, 21, 2, 20, 1], "vertices": [2, 40, 36.95, 33.31, 0.91666, 41, 68.52999, 41.04999, 0.08332, 2, 40, 66.01999, 20.35, 0.76813, 41, 41.40999, 24.38999, 0.23185, 2, 40, 74.51, 16.56999, 0.64468, 41, 33.49, 19.53, 0.3553, 2, 40, 70.88999, 21.96999, 0.29071, 41, 39.99, 19.45999, 0.70928, 3, 40, 42.77, 63.88999, 0.11483, 41, 90.47, 18.95, 0.60853, 49, -17.2, 9, 0.2766, 2, 41, 101.86, 18.82999, 0.45954, 49, -14.38, 20.04, 0.54044, 2, 41, 106.47, 2.07999, 0.0625, 49, 2.98, 20.55999, 0.9375, 1, 49, 51.31999, 21.97999, 1, 1, 49, 60.40999, 11.1, 1, 1, 49, 72.38999, 9.60999, 1, 1, 49, 100.36, -23.87, 1, 1, 49, 104.95999, -40.9, 1, 1, 49, 78.37, -25.61, 1, 1, 49, 86.05, -56.84, 1, 1, 49, 52.91999, -30.04, 1, 2, 41, 62.24, -43.91999, 0.0625, 49, 37.18999, -33.33, 0.9375, 2, 41, 64.88999, -28.64999, 0.3125, 49, 22.97999, -27.13999, 0.6875, 2, 41, 57.68999, -27.17, 0.30612, 49, 19.82999, -33.77999, 0.69387, 2, 40, 124.19, 3.82999, 0.19394, 41, -5.09, -14.22999, 0.80603, 2, 40, 110.76999, -19.64999, 0.3125, 41, -16.87999, 10.1, 0.6875, 2, 40, 99.13999, -19.2, 0.51612, 41, -9.93, 19.44, 0.48385, 2, 40, 43.72999, -17.03, 0.9375, 41, 23.17, 63.91999, 0.0625, 1, 40, 35.40999, -29.77, 1, 1, 40, -15.68, -28.02, 1, 1, 40, -13.86999, 24.64999, 1], "edges": [44, 46, 44, 42, 38, 36, 32, 30, 30, 28, 28, 26, 24, 22, 18, 16, 16, 14, 46, 48, 38, 4, 6, 4, 6, 36, 42, 40, 40, 38, 4, 2, 2, 0, 40, 2, 10, 32, 36, 34, 34, 32, 10, 8, 8, 6, 34, 8, 14, 12, 12, 10, 12, 30, 18, 20, 22, 20, 26, 24, 48, 0]}}, "raptor_body": {"raptor_body": {"type": "mesh", "hull": 46, "width": 1219, "height": 570, "uvs": [0.89014, 0.11136, 1, 0.22194, 1, 0.42847, 0.88179, 0.38589, 0.874, 0.47986, 0.84783, 0.51728, 0.82504, 0.54984, 0.82403, 0.61606, 0.82305, 0.67972, 0.74042, 0.86709, 0.61596, 0.93097, 0.49649, 0.90968, 0.41186, 0.71379, 0.36955, 0.70086, 0.32823, 0.68824, 0.30082, 0.69962, 0.27515, 0.71028, 0.25301, 0.71948, 0.22568, 0.73082, 0.20832, 0.72362, 0.19092, 0.7164, 0.15952, 0.70337, 0.1301, 0.69116, 0.09227, 0.67546, 0.06029, 0.63165, 0.02855, 0.58817, 0, 0.49874, 0.05045, 0.53494, 0.08267, 0.54507, 0.11815, 0.55623, 0.14733, 0.54161, 0.17913, 0.52568, 0.20324, 0.5136, 0.22867, 0.50087, 0.24871, 0.47664, 0.27523, 0.44458, 0.32026, 0.39015, 0.37517, 0.35747, 0.43476, 0.32201, 0.4893, 0.35534, 0.56021, 0.39867, 0.61587, 0.40674, 0.67769, 0.4157, 0.69094, 0.31314, 0.69362, 0.14742, 0.79219, 0.08354, 0.51541, 0.74573, 0.62393, 0.75425, 0.70856, 0.7287, 0.76132, 0.63288, 0.7566, 0.49454, 0.80613, 0.27517, 0.65885, 0.59037, 0.53929, 0.54937, 0.42632, 0.52207, 0.3246, 0.55241, 0.22715, 0.618, 0.10574, 0.61341, 0.03969, 0.56109, 0.77916, 0.39461, 0.37556, 0.53721, 0.27743, 0.58416, 0.16958, 0.61582, 0.07259, 0.58715, 0.87545, 0.31683, 0.85488, 0.21417, 0.81012, 0.17403, 0.83214, 0.25662, 0.83823, 0.32214, 0.84622, 0.41719, 0.59954, 0.57003, 0.49074, 0.53763, 0.76917, 0.43888, 0.75912, 0.56845, 0.871, 0.3701, 0.85431, 0.43545, 0.89558, 0.32412, 0.90105, 0.22877, 0.91523, 0.20564, 0.93086, 0.219, 0.93446, 0.25858, 0.91956, 0.2776, 0.9061, 0.26423, 0.9415, 0.25929, 0.93589, 0.21545, 0.91669, 0.19192, 0.89297, 0.22201, 0.90245, 0.28513, 0.92006, 0.281, 0.92143, 0.29619, 0.94856, 0.2643, 0.19894, 0.61694, 0.13973, 0.61469, 0.25158, 0.60156, 0.88779, 0.26675], "triangles": [13, 60, 12, 12, 71, 46, 46, 70, 47, 11, 12, 46, 47, 11, 46, 10, 11, 47, 13, 14, 55, 15, 93, 14, 16, 56, 15, 58, 26, 27, 28, 58, 27, 63, 28, 29, 63, 58, 28, 25, 26, 58, 25, 58, 63, 57, 63, 29, 92, 29, 30, 57, 29, 92, 24, 25, 63, 24, 63, 57, 23, 24, 57, 92, 23, 57, 22, 23, 92, 62, 22, 92, 21, 22, 62, 20, 21, 91, 92, 30, 62, 91, 21, 62, 62, 30, 31, 91, 62, 31, 31, 32, 93, 20, 91, 56, 56, 19, 20, 17, 19, 56, 18, 19, 17, 93, 91, 31, 55, 34, 35, 33, 34, 55, 61, 33, 55, 61, 32, 33, 93, 32, 61, 56, 91, 93, 56, 93, 15, 16, 17, 56, 36, 37, 54, 60, 35, 36, 54, 60, 36, 60, 55, 35, 61, 55, 14, 93, 61, 14, 60, 13, 55, 12, 60, 54, 39, 54, 37, 39, 37, 38, 71, 54, 39, 40, 71, 39, 53, 71, 40, 53, 40, 41, 12, 54, 71, 46, 71, 53, 66, 45, 0, 44, 45, 66, 1, 84, 85, 65, 66, 0, 0, 85, 65, 85, 86, 65, 85, 0, 1, 78, 85, 84, 79, 78, 84, 78, 86, 85, 77, 86, 78, 77, 78, 79, 67, 66, 65, 83, 80, 79, 84, 83, 79, 77, 79, 80, 84, 90, 83, 82, 77, 80, 1, 90, 84, 94, 65, 86, 94, 86, 77, 94, 77, 82, 67, 65, 94, 51, 44, 66, 51, 66, 67, 81, 82, 80, 83, 88, 81, 87, 94, 82, 87, 82, 81, 87, 81, 88, 88, 90, 89, 87, 88, 89, 80, 83, 81, 90, 88, 83, 43, 44, 51, 64, 67, 94, 64, 94, 87, 68, 67, 64, 51, 67, 68, 76, 64, 87, 76, 87, 89, 74, 68, 64, 74, 64, 76, 3, 74, 76, 59, 43, 51, 59, 51, 68, 69, 59, 68, 74, 69, 68, 69, 74, 3, 90, 1, 2, 89, 90, 2, 76, 89, 2, 3, 76, 2, 75, 69, 3, 4, 75, 3, 75, 72, 69, 5, 75, 4, 5, 72, 75, 72, 59, 69, 59, 42, 43, 72, 42, 59, 50, 42, 72, 50, 72, 5, 6, 50, 5, 73, 50, 6, 52, 70, 42, 50, 52, 42, 73, 52, 50, 7, 73, 6, 49, 73, 7, 52, 73, 49, 8, 49, 7, 48, 52, 49, 48, 49, 8, 47, 52, 48, 70, 53, 41, 70, 41, 42, 46, 53, 70, 47, 70, 52, 9, 48, 8, 47, 48, 9, 10, 47, 9], "vertices": [1, 8, 147.48, -145.48, 1, 1, 8, 89.4, -281.62, 1, 1, 8, -28.23999, -285.93, 1, 1, 8, -14.57999, -194.68, 1, 4, 6, 238.38998, -84.12999, 0.2085, 7, 32.09999, -140.85, 0.19336, 8, -61.95999, -132.26, 0.42114, 39, 129.57, 6.38999, 0.17696, 5, 2, 332.7, 63.70999, 0.06904, 6, 199.57, -83.02999, 0.29423, 7, 3.69, -114.37, 0.2194, 8, -85.43, -101.31999, 0.30858, 39, 127.33999, -26.63999, 0.10869, 5, 2, 307.08, 43.5, 0.11017, 6, 166.95, -82.12999, 0.37281, 7, -20.18, -92.13999, 0.24571, 8, -105.18, -75.33999, 0.21862, 39, 123.08, -64.79, 0.05263, 4, 2, 307.75, 5.69999, 0.18626, 6, 143.25, -111.58999, 0.58008, 7, -57.43, -98.56999, 0.12363, 8, -142.98, -75.33, 0.10999, 2, 2, 308.7, -30.54999, 0.25, 6, 120.75, -140.04, 0.75, 2, 2, 213.94, -142.7, 0.75, 6, -23.82999, -165.45, 0.25, 3, 2, 64.44999, -187.34, 0.31139, 58, -158.45, 158.33, 0.10379, 1, 84.16, -190.98, 0.5848, 1, 1, -61.47, -178.84, 1, 4, 58, 118.47, 114.73999, 0.07383, 59, 17.17, 122.48999, 0.17504, 60, -100.70999, 132.55, 0.06818, 1, -166.91, -67.94999, 0.68294, 4, 58, 170.4, 123.12999, 0.06537, 59, 66.70999, 104.76999, 0.20998, 60, -53.08, 110.20999, 0.12015, 1, -217.69, -61.33, 0.60447, 6, 58, 221.11, 131.31, 0.02178, 59, 115.06999, 87.47, 0.23058, 60, -6.57999, 88.38999, 0.16257, 61, -168.91998, 31, 0.02826, 62, -282.82, -90.19, 0.02899, 1, -267.66, -55.13999, 0.52776, 5, 59, 146.51, 86.08, 0.23634, 60, 26.65999, 83.38999, 0.19337, 61, -134.99, 41.34, 0.06624, 62, -257.52, -60.65, 0.06962, 1, -298.87, -61.99, 0.43435, 5, 59, 178.73, 86.41, 0.2252, 60, 56.68, 81.29, 0.20996, 61, -107.12999, 46.31, 0.1127, 62, -232.44, -51.25999, 0.12148, 1, -328.68, -69.23999, 0.33059, 5, 59, 203.26, 86.51, 0.21603, 60, 83.05999, 77.01999, 0.22457, 61, -79.55999, 53.52999, 0.13939, 62, -210.88998, -28.29999, 0.15618, 1, -354.01, -75.41, 0.26379, 5, 59, 238.06, 85.41, 0.20114, 60, 115.65, 74.66, 0.23149, 61, -49.52999, 60.58, 0.16455, 62, -185.49, -14.97999, 0.19122, 1, -385.33, -83.15, 0.21155, 5, 59, 255.33, 78.84999, 0.18223, 60, 133.83, 63.18, 0.23194, 61, -27.04, 56.84, 0.18907, 62, -163.58, -5.26, 0.22657, 1, -406.45, -79.88999, 0.17014, 5, 59, 275.48, 71.62, 0.16168, 60, 152.97, 53.58, 0.22885, 61, -5.82, 53.93999, 0.21291, 62, -142.85, 0.10999, 0.26159, 1, -427.72, -77.47, 0.13492, 5, 59, 313.81, 53.61, 0.14198, 60, 188.04, 35.81999, 0.22292, 61, 31.84, 49.29999, 0.23477, 62, -106.45999, 7.48999, 0.29326, 1, -465.96, -72.58999, 0.10701, 5, 59, 345.74, 45.54, 0.12501, 60, 219.6, 19.28, 0.21789, 61, 68.30999, 43.02, 0.25622, 62, -70.12999, 18.19, 0.32247, 1, -502.09, -68.19, 0.07835, 4, 59, 390.81, 21.29999, 0.11757, 60, 261.62, -3.66, 0.22607, 61, 114.55, 37.83, 0.29087, 62, -26.14999, 30.34, 0.36546, 4, 59, 423.87, -11.10999, 0.10536, 60, 291.46, -39.06, 0.21953, 61, 154.83, 14.98999, 0.30098, 62, 19.90999, 25.67, 0.37408, 4, 59, 456.68, -43.27, 0.09949, 60, 321.06, -74.19999, 0.21749, 61, 194.79, -7.65999, 0.30788, 62, 65.62, 21.04, 0.37512, 4, 59, 480.34, -100.27999, 0.10045, 60, 339.2, -133.2, 0.22025, 61, 232.3, -56.68999, 0.31103, 62, 119.69999, -8.68999, 0.36823, 4, 59, 424.34, -67.51999, 0.10379, 60, 286.57, -95.26999, 0.23086, 61, 169.77, -39.4, 0.30827, 62, 55.50999, -18.07999, 0.35705, 4, 59, 387.08, -53.84, 0.11505, 60, 250.77, -78.11, 0.24539, 61, 130.24, -35.75, 0.30092, 62, 17.87, -30.67, 0.3386, 4, 59, 346.04, -38.77999, 0.1347, 60, 211.34, -59.22, 0.26271, 61, 86.69999, -31.71999, 0.2888, 62, -23.59, -44.54, 0.31376, 5, 58, 449.17, 125.97, 0.02418, 59, 311.45, -35.25, 0.16383, 60, 175.88998, -56.83, 0.2727, 61, 51.52999, -43.13999, 0.26317, 62, -52.88, -67.87, 0.27606, 5, 58, 418.38, 93.72, 0.05913, 59, 269.72, -40.63999, 0.19681, 60, 135.19, -53.81999, 0.27687, 61, 13.42, -53.11, 0.23218, 62, -82.02999, -93.66, 0.23497, 5, 58, 390.82, 86.58, 0.10638, 59, 241.19, -39.79999, 0.2354, 60, 105.58999, -52.93, 0.27331, 61, -16.25, -62.15999, 0.19459, 62, -108.33999, -111.23999, 0.19028, 5, 58, 364.8, 62.47999, 0.14347, 59, 207.71, -42.13999, 0.28099, 60, 73.33, -49.43, 0.26866, 61, -46.11, -70.48999, 0.15763, 62, -129.51, -133.56, 0.1492, 5, 58, 345.49, 47.52999, 0.18499, 59, 182.34, -50.61999, 0.33114, 60, 45.86999, -56.61999, 0.25881, 61, -71.56999, -84.95999, 0.11739, 62, -150.85, -153.35, 0.10762, 5, 58, 319.95, 15.14999, 0.23755, 59, 145.6, -61.95, 0.39395, 60, 9.60999, -63.25999, 0.24618, 61, -101.05999, -105.58, 0.06443, 62, -165.65, -187.83, 0.05784, 3, 58, 276.58, -30.61, 0.29941, 59, 85.51999, -81.11, 0.46773, 60, -52.00999, -76.62, 0.23283, 3, 58, 214.5, -70.36, 0.32855, 59, 11.97, -85.98, 0.486, 60, -125.69, -74.48, 0.18543, 2, 58, 147.13998, -113.5, 0.59565, 59, -67.83999, -91.26, 0.40432, 2, 2, -113.13999, 135.84, 0.24191, 58, 91.72, -112.58999, 0.75806, 2, 2, -42.11999, 116.76999, 0.14515, 58, 18.2, -111.16999, 0.85483, 1, 2, 44.2, 107.09999, 1, 2, 2, 140.09, 96.34999, 0.22578, 6, 72.58999, 65.41, 0.7742, 4, 2, 137.69, 169.35, 0.05643, 6, 117.5, 123, 0.24355, 7, 78.3, 94.48, 0.2125, 8, 23.7, 91.73999, 0.4875, 2, 7, 171.15, 111.98, 0.25, 8, 118.16999, 93.15, 0.75, 1, 8, 158.96, -25.57999, 1, 1, 1, -40.63, -86.01, 1, 3, 2, 67.33999, -86.66, 0.33215, 58, -137.02, 59.91999, 0.08303, 1, 92.54, -90.61, 0.5848, 2, 2, 170.13, -66.29, 0.75, 6, -8.52999, -78.72, 0.25, 2, 2, 231.74, -8.11999, 0.4, 6, 76.02999, -73.51999, 0.6, 4, 2, 222.04, 70.41, 0.17551, 6, 118.9, -7, 0.55822, 7, -6.57999, -3.99, 0.17738, 8, -76.73, 9.18, 0.08884, 1, 8, 50.43, -46.56, 1, 1, 6, -9.88, 20.64999, 1, 2, 2, -53.22, 20.53, 0.2, 58, 5.8, -15.09, 0.8, 3, 2, -180.7, 32.22, 0.08563, 58, 132.35, 4.23999, 0.56215, 59, -23.97999, 19.01, 0.35218, 3, 58, 246.38998, 57.52999, 0.30272, 59, 101.61, 10.64999, 0.46988, 60, -27.28, 13.19999, 0.22736, 5, 58, 346.99, 126.84999, 0.13479, 59, 223.16998, 22.82999, 0.28279, 60, 94.87999, 13.77, 0.24481, 61, -47.84999, -3.72, 0.17321, 62, -158.02, -73.16, 0.16437, 4, 59, 367.51, -9.96, 0.14655, 60, 235.45, -32.56999, 0.23072, 61, 100.05999, 1.62, 0.29607, 62, -24.80999, -8.63, 0.3266, 4, 59, 440.24, -55.59999, 0.1082, 60, 303.52, -84.91, 0.21897, 61, 182.07, -23.79999, 0.31097, 62, 60.47999, 1.13999, 0.36182, 3, 6, 174.99, 22.21999, 0.2, 7, 54.81999, -19.13999, 0.6, 8, -18.79999, -16.2, 0.2, 3, 58, 189.25, 30.82999, 0.33252, 59, 38.68, 14.84, 0.48076, 60, -89.51999, 23.34, 0.18669, 5, 58, 295.08, 91.08, 0.2289, 59, 160.45, 16.54, 0.38754, 60, 31.85, 13.47999, 0.23059, 61, -106.86, -25.88999, 0.08042, 62, -203.08, -117.23999, 0.07249, 5, 58, 414.43, 146.25, 0.07359, 59, 291.61, 7.26999, 0.20197, 60, 161.52998, -8.19999, 0.23761, 61, 22.27, -1.17999, 0.24023, 62, -94.86, -42.56, 0.24653, 4, 59, 404.01, -32.86999, 0.12351, 60, 269.61, -58.84, 0.2226, 61, 141.21, -11.13, 0.30608, 62, 17.97999, -3.72, 0.34777, 1, 8, 26.39999, -166.06, 1, 1, 8, 87.20999, -106.12, 1, 1, 8, 108.19, -49.61999, 1, 2, 8, 61.72999, -82.12999, 0.5002, 39, 4.42, 52.83, 0.49977, 2, 8, 22.84, -109.4, 0.5002, 39, 51.52, 46.72999, 0.49977, 4, 6, 247.12, -50.52, 0.06544, 7, 60.86, -121.4, 0.06069, 8, -30.29999, -118, 0.49079, 39, 96.58, 17.21999, 0.38304, 1, 2, 26.72999, 14.8, 1, 2, 2, -107.97, 25.67, 0.24191, 58, 60.16999, -6.90999, 0.75806, 4, 2, 235.52998, 102.95999, 0.07609, 6, 150.1, 9.35, 0.35532, 7, 27.63999, -12.34, 0.41675, 8, -44.43, -4.86999, 0.1518, 3, 2, 227.15, 28.48999, 0.31014, 6, 95.95999, -42.45999, 0.60548, 7, -47.22999, -15.43999, 0.08434, 2, 8, 5.19, -153.1, 0.87617, 39, 90.95999, 71.20999, 0.12381, 4, 6, 243.13, -60.59, 0.10668, 7, 51.20999, -126.33, 0.09893, 8, -40.65, -121.20999, 0.47071, 39, 105.70999, 17.32999, 0.32363, 1, 8, 23.69, -185.21, 1, 1, 8, 79.63999, -175.94, 1, 1, 8, 93.95999, -187.56, 1, 1, 8, 87.06999, -206.55, 1, 1, 8, 64.19999, -216.74, 1, 1, 8, 52.22999, -203.68, 1, 1, 8, 59.24, -187.02998, 1, 1, 8, 64.26, -223.8, 1, 1, 8, 89.44, -211.41, 1, 1, 8, 102.04, -186.95, 1, 1, 8, 83.09999, -166.13998, 1, 1, 8, 46.84, -186.41, 1, 1, 8, 50.31999, -204.36, 1, 1, 8, 41.7, -206.59, 1, 1, 8, 61.86999, -230.97, 1, 5, 58, 374.97, 143.6, 0.1029, 59, 256.29, 17.42, 0.23984, 60, 127.43, 2.06999, 0.2431, 61, -13.35, -3.04999, 0.20853, 62, -128.13998, -55.45999, 0.20556, 4, 59, 327.21, 4.42, 0.17789, 60, 196.27998, -19.31999, 0.24247, 61, 58.70999, -1.04999, 0.28055, 62, -62.24, -26.20999, 0.29905, 5, 58, 318.32, 113.62, 0.17773, 59, 192.26, 20.13999, 0.33382, 60, 64.19, 12.43999, 0.24171, 61, -76.55, -13.67, 0.12849, 62, -182.56, -89.30999, 0.11821, 2, 8, 56.97999, -162.99, 0.89258, 39, 57.54, 112, 0.1074], "edges": [22, 20, 20, 18, 18, 16, 6, 4, 4, 2, 90, 88, 54, 52, 52, 50, 24, 22, 88, 86, 86, 84, 8, 6, 24, 26, 26, 28, 72, 74, 74, 76, 70, 72, 46, 48, 48, 50, 54, 56, 56, 58, 80, 82, 82, 84, 76, 78, 78, 80, 8, 10, 10, 12, 12, 14, 14, 16, 0, 90, 0, 2, 62, 64, 64, 66, 40, 42, 58, 60, 60, 62, 42, 44, 44, 46, 66, 68, 68, 70, 32, 34, 34, 36, 52, 116, 116, 126, 126, 114, 114, 184, 184, 124, 124, 182, 182, 112, 112, 186, 186, 122, 122, 110, 110, 120, 120, 108, 108, 142, 142, 106, 106, 140, 140, 104, 92, 94, 94, 96, 96, 98, 98, 146, 146, 100, 100, 144, 144, 118, 118, 102, 8, 150, 150, 138, 138, 136, 136, 134, 134, 132, 156, 154, 154, 164, 164, 162, 162, 160, 160, 158, 158, 156, 180, 178, 178, 174, 174, 188, 188, 172, 170, 168, 28, 30, 30, 32, 36, 38, 38, 40]}}, "raptor_front_arm": {"raptor_front_arm": {"type": "mesh", "hull": 27, "width": 162, "height": 203, "uvs": [0.39562, 0.1396, 0.3877, 0.30212, 0.3123, 0.41784, 0.27287, 0.47835, 0.33388, 0.4507, 0.54879, 0.35328, 0.64092, 0.31152, 0.73024, 0.36529, 1, 0.5277, 1, 0.86606, 0.93242, 1, 0.86176, 0.80967, 0.75576, 0.99765, 0.71748, 1, 0.70276, 0.77442, 0.62031, 0.73448, 0.58792, 0.64519, 0.53561, 0.6582, 0.13448, 0.75798, 0, 0.69218, 0.01846, 0.56357, 0.05498, 0.30917, 0, 0.27863, 0, 0.12423, 0, 0, 0.19596, 0, 0.40242, 0, 0.24536, 0.1924, 0.21678, 0.0811], "triangles": [0, 28, 26, 23, 25, 28, 28, 25, 26, 23, 24, 25, 6, 7, 16, 6, 16, 5, 15, 16, 7, 7, 14, 15, 8, 14, 7, 11, 14, 8, 11, 8, 9, 12, 14, 11, 13, 14, 12, 10, 11, 9, 17, 4, 5, 16, 17, 5, 18, 19, 3, 18, 3, 4, 18, 4, 17, 27, 28, 0, 27, 22, 23, 27, 23, 28, 1, 27, 0, 21, 22, 27, 21, 27, 1, 2, 21, 1, 2, 20, 21, 3, 20, 2, 19, 20, 3], "vertices": [2, 21, 3.05999, 31.87999, 0.51074, 6, 66.55999, -109.48, 0.48923, 1, 21, 35.86999, 35.61999, 1, 2, 21, 60.93999, 27.12, 0.8464, 22, 46.49, 31.12, 0.15358, 2, 21, 74.05, 22.67, 0.3492, 22, 36.5, 21.53, 0.65078, 3, 21, 67, 31.57999, 0.10937, 22, 47.65999, 23.68, 0.78125, 31, -40.93, -19.44, 0.10937, 2, 22, 86.98, 31.23999, 0.65078, 31, -25.75, 17.61, 0.3492, 2, 22, 103.83, 34.49, 0.34375, 31, -19.23999, 33.49, 0.65625, 2, 22, 114.04, 19.51, 0.10937, 31, -1.11, 33.84, 0.89061, 1, 31, 53.61999, 34.88, 1, 1, 31, 96.02999, -19.15999, 1, 1, 31, 104.19999, -47.31, 1, 1, 31, 71.33999, -23.97999, 1, 1, 31, 81.38999, -64.61, 1, 1, 31, 76.8, -68.80999, 1, 1, 31, 46.65, -34.25, 1, 2, 22, 73.12999, -45.75999, 0.10937, 31, 31.13999, -36.11999, 0.89061, 2, 22, 73.98, -26.89999, 0.34375, 31, 15.81999, -25.09, 0.65625, 2, 22, 65.09999, -26.69, 0.65078, 31, 10.77999, -32.40999, 0.3492, 3, 21, 133.56, 9.13, 0.10937, 22, -2.94, -25.03, 0.78125, 31, -27.84, -88.47, 0.10937, 2, 21, 123.66999, -14.42, 0.3492, 22, -19.29, -5.38999, 0.65078, 2, 21, 97.41, -15.43, 0.8464, 22, -8.07999, 18.37, 0.15358, 1, 21, 45.45999, -17.43, 1, 2, 21, 40.68999, -27.17, 0.45034, 6, -1.69, -93.8, 0.54963, 2, 21, -2.74, -29.62999, 0.44352, 6, 18.98999, -72.93, 0.55646, 1, 6, 32.11, -48.45, 1, 1, 6, 57.56, -67.43, 1, 1, 6, 84.37999, -87.41999, 1, 2, 21, 16.44, 5.21, 0.7182, 6, 46.31, -101.86, 0.28178, 2, 21, -4.51, 5.32, 0.48851, 6, 52.81999, -81.94, 0.51147], "edges": [38, 36, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 44, 42, 38, 6, 38, 40, 40, 42, 6, 4, 4, 2, 40, 4, 8, 6, 36, 8, 32, 12, 42, 2, 52, 0, 0, 2, 16, 14, 14, 12, 30, 14, 36, 34, 34, 32, 12, 10, 10, 8, 34, 10, 48, 50, 50, 52, 44, 46, 46, 48, 50, 56, 56, 54]}}, "raptor_front_leg": {"raptor_front_leg": {"type": "mesh", "hull": 32, "width": 382, "height": 514, "uvs": [0.55116, 0.17817, 0.6279, 0.36027, 0.6671, 0.4533, 0.64879, 0.51527, 0.53553, 0.56893, 0.32335, 0.66946, 0.28674, 0.72086, 0.32538, 0.804, 0.36258, 0.80144, 0.42056, 0.79744, 0.61015, 0.78435, 0.84813, 0.84028, 1, 0.93854, 0.62439, 0.91738, 0.72812, 1, 0.58574, 1, 0.36707, 0.96667, 0.26306, 0.95082, 0.16266, 0.93552, 0.03859, 0.72237, 0, 0.66946, 0.0374, 0.62999, 0.1647, 0.49562, 0.23731, 0.4568, 0.27019, 0.43923, 0.28063, 0.43364, 0.223, 0.4057, 0.12565, 0.35851, 0, 0.29759, 0, 0.1524, 0, 0, 0.32132, 0, 0.32222, 0.22778, 0.4493, 0.38031, 0.47664, 0.44361, 0.4615, 0.47375, 0.35106, 0.53247, 0.20091, 0.65256, 0.18527, 0.72148, 0.25222, 0.86314, 0.30941, 0.88124, 0.55694, 0.89613, 0.55857, 0.89207, 0.47493, 0.85339, 0.6059, 0.91526, 0.39705, 0.89129, 0.13229, 0.09352, 0.36997, 0.45345, 0.37163, 0.43827, 0.32515, 0.39424, 0.23759, 0.34425, 0.34065, 0.47414], "triangles": [46, 30, 31, 41, 42, 44, 43, 9, 10, 42, 43, 10, 41, 43, 42, 10, 13, 42, 11, 13, 10, 13, 11, 12, 13, 44, 42, 14, 15, 13, 45, 8, 9, 45, 40, 8, 16, 40, 45, 17, 40, 16, 16, 45, 15, 15, 45, 44, 45, 41, 44, 15, 44, 13, 45, 9, 43, 45, 43, 41, 19, 21, 38, 20, 21, 19, 39, 38, 6, 39, 6, 7, 40, 39, 7, 40, 7, 8, 18, 19, 38, 18, 38, 39, 17, 39, 40, 18, 39, 17, 47, 25, 48, 24, 25, 47, 35, 48, 34, 47, 48, 35, 51, 24, 47, 23, 24, 51, 3, 34, 2, 35, 34, 3, 36, 51, 47, 36, 47, 35, 4, 35, 3, 36, 35, 4, 37, 22, 23, 21, 22, 37, 36, 37, 23, 36, 23, 51, 5, 37, 36, 5, 36, 4, 6, 37, 5, 38, 21, 37, 38, 37, 6, 29, 30, 46, 32, 31, 0, 46, 31, 32, 28, 29, 46, 46, 27, 28, 32, 50, 46, 50, 27, 46, 33, 32, 0, 33, 0, 1, 49, 50, 32, 33, 49, 32, 26, 27, 50, 26, 50, 49, 25, 26, 49, 48, 49, 33, 25, 49, 48, 34, 33, 1, 48, 33, 34, 34, 1, 2], "vertices": [2, 25, 128.02998, 88.47, 0.85039, 1, 158.83, -71.91, 0.14959, 2, 25, 219.55, 53.15, 0.77987, 26, -48.04, -38.58, 0.2201, 2, 25, 266.3, 35.09999, 0.53544, 26, -36.72999, 10.22, 0.46454, 2, 25, 286.89, 9.78999, 0.35166, 26, -14.56, 34.13999, 0.64832, 2, 25, 281.54, -41.24, 0.09228, 26, 36.70999, 36, 0.90771, 3, 25, 271.53, -136.86, 0.05785, 26, 132.77, 39.47999, 0.71425, 27, 34.99, 78.76, 0.22787, 3, 26, 158.21, 55.16999, 0.53079, 27, 52.65, 54.63, 0.38143, 28, 7.01, 85.54, 0.08776, 5, 26, 167.13998, 99.48999, 0.21498, 27, 97.55, 49.25, 0.35357, 28, 28.71999, 45.86999, 0.14219, 29, -21.26, 49.99, 0.22491, 30, -72.29, 25.95999, 0.0643, 5, 26, 154.22, 105.55, 0.07536, 27, 102.56999, 62.59999, 0.2305, 28, 42.50999, 49.54999, 0.28378, 29, -7.05999, 51.38999, 0.27004, 30, -58.16999, 28.03, 0.14025, 4, 27, 109.72, 83.38999, 0.11607, 28, 64.08999, 55.22999, 0.08122, 29, 15.11999, 53.50999, 0.36961, 30, -36.09, 31.19, 0.43307, 1, 30, 35.79999, 41.81, 1, 1, 30, 128.11, 17.93, 1, 1, 30, 188.72, -29.42, 1, 2, 29, 93.29, -7.59999, 0.47999, 30, 44.86, -26.17, 0.51999, 2, 29, 133.16998, -49.83, 0.776, 30, 86.69, -66.47, 0.22398, 2, 29, 78.77999, -50.15, 0.76798, 30, 32.38, -69.36, 0.23199, 1, 29, -4.90999, -33.54999, 1, 3, 27, 155.04, -5.13, 0.35916, 28, 17.87999, -32.5, 0.30632, 29, -44.61999, -25.61, 0.33449, 4, 26, 254.98, 126.26999, 0.10153, 27, 131.21, -36.2, 0.54211, 28, -21.23999, -31.17, 0.20872, 29, -83.01999, -17.96999, 0.14759, 3, 26, 240.33, 7.80999, 0.25586, 27, 11.93999, -30.97999, 0.61614, 28, -86.30999, 68.9, 0.12797, 2, 26, 239.26, -23.1, 0.45486, 27, -18.95999, -32.36999, 0.54513, 3, 25, 187.65, -209.73, 0.09776, 26, 216.66, -33.34999, 0.58891, 27, -30.96999, -10.64999, 0.3133, 2, 25, 163.85, -128.66998, 0.19603, 26, 139.75, -68.26, 0.80396, 2, 25, 165.74, -94.48999, 0.31962, 26, 105.58999, -71.26, 0.68036, 2, 25, 166.38998, -79.06999, 0.46224, 26, 90.23, -72.76, 0.53773, 2, 25, 166.49, -74.16999, 0.53785, 26, 85.41999, -73.27999, 0.46213, 2, 25, 141.54, -82.45999, 0.73137, 26, 97.12999, -96.81999, 0.26861, 2, 25, 99.76, -97.08, 0.85324, 26, 117.33999, -136.23, 0.14675, 2, 25, 45.00999, -114.55999, 0.83614, 1, -51.09, -135.29, 0.16383, 2, 25, -16.2, -74.76, 0.62989, 1, -42.95, -58.38, 0.37009, 2, 25, -74.73, -19.32999, 0.31468, 1, -52.65999, 17.54999, 0.68531, 2, 25, 1.66999, 76.75, 0.25576, 1, 70.06999, 18.78, 0.74422, 1, 25, 93.54, 4.13, 1, 2, 25, 185.13998, -6.65999, 0.75461, 26, 15.97999, -64.26999, 0.24537, 2, 25, 217.11, -18.75, 0.50844, 26, 23.46999, -30.93, 0.49154, 2, 25, 225.63, -32.91999, 0.32526, 26, 36.29999, -20.5, 0.67471, 2, 25, 223, -84.73, 0.20192, 26, 87.95999, -15.85999, 0.79807, 3, 25, 235.61, -168.06, 0.08089, 26, 168.69, 8.28999, 0.57147, 27, 6.73999, 40.47, 0.3476, 3, 26, 191.79, 35.79999, 0.32545, 27, 36, 19.62, 0.57243, 28, -31.13999, 78.73999, 0.1021, 4, 26, 206.63998, 111.52999, 0.10807, 27, 112.69, 10.81999, 0.52066, 28, 6.25, 11.22999, 0.23517, 29, -49.02999, 19.43, 0.13605, 3, 27, 130.6, 26.40999, 0.35067, 28, 29.35, 5.71, 0.2824, 29, -27.12, 10.25, 0.36689, 2, 29, 67.45999, 3.16, 0.384, 30, 18.54999, -16.62999, 0.61598, 1, 30, 19.06999, -14.51, 1, 2, 29, 36, 24.95, 0.384, 30, -13.89, 3.64, 0.61598, 2, 29, 86.23, -6.55, 0.48798, 30, 37.75, -25.45999, 0.512, 4, 26, 164.9, 153.55, 0.02263, 27, 151.18, 56, 0.23907, 28, 65.44, 5.55, 0.19254, 29, 8.44999, 4.26999, 0.54574, 2, 25, -9.27999, -17.5, 0.59605, 1, 7.71999, -30.85, 0.40393, 2, 25, 195.9, -53.81, 0.42368, 26, 61.11, -47.06, 0.5763, 2, 25, 190.1, -48.45, 0.53231, 26, 56.61, -53.56, 0.46768, 2, 25, 161.26, -48.25999, 0.79873, 26, 60.43999, -82.12999, 0.20126, 2, 25, 120.37, -58.54, 0.85455, 26, 76.30999, -121.18, 0.14543, 2, 25, 197.37, -69.23, 0.3355, 26, 76.16999, -43.45999, 0.66448], "edges": [40, 38, 38, 36, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 14, 12, 12, 10, 6, 4, 60, 62, 0, 62, 40, 42, 42, 44, 34, 36, 16, 14, 52, 50, 4, 2, 2, 0, 10, 8, 8, 6, 72, 74, 74, 76, 76, 78, 78, 80, 80, 90, 90, 88, 16, 18, 18, 20, 30, 32, 32, 34, 56, 58, 58, 60, 94, 96, 96, 98, 52, 54, 54, 56, 100, 98, 48, 50, 44, 46, 46, 48, 102, 94, 72, 70, 70, 68, 66, 68, 66, 64, 64, 92, 86, 84, 50, 96, 94, 48, 46, 102, 52, 98, 54, 100, 70, 6, 68, 4, 66, 2, 72, 8, 86, 20, 86, 82, 82, 88, 84, 26, 88, 26]}}, "raptor_hindleg_back": {"raptor_hindleg_back": {"type": "mesh", "hull": 36, "width": 338, "height": 429, "uvs": [0.45041, 0.09352, 0.56933, 0.23361, 0.65294, 0.47296, 0.66353, 0.50822, 0.63174, 0.54254, 0.32383, 0.69723, 0.30068, 0.73875, 0.27934, 0.77704, 0.30417, 0.83513, 0.31058, 0.85014, 0.341, 0.85046, 0.45165, 0.85163, 0.59555, 0.81881, 0.91176, 0.92548, 1, 1, 0.56336, 0.96426, 0.48349, 0.9826, 0.29878, 0.98027, 0.22808, 0.98389, 0.15997, 0.98737, 0.15423, 0.95546, 0.13894, 0.87047, 0.07371, 0.78726, 0, 0.75299, 0, 0.7049, 0, 0.671, 0.11875, 0.64652, 0.16535, 0.52659, 0.28495, 0.47397, 0.2901, 0.45773, 0.29427, 0.4446, 0.20635, 0.40396, 0.06128, 0.33691, 0, 0.25247, 0, 0, 0.30793, 0, 0.27599, 0.20261, 0.40397, 0.31121, 0.48439, 0.45963, 0.48317, 0.48383, 0.47029, 0.51062, 0.22698, 0.67328, 0.17141, 0.7242, 0.17122, 0.78241, 0.22995, 0.89469, 0.24677, 0.90829, 0.28672, 0.9146, 0.46582, 0.91414], "triangles": [15, 13, 14, 16, 47, 15, 15, 12, 13, 15, 47, 12, 18, 46, 17, 18, 45, 46, 17, 47, 16, 17, 46, 47, 47, 10, 11, 47, 46, 10, 47, 11, 12, 45, 18, 19, 44, 45, 20, 20, 45, 19, 20, 21, 44, 46, 9, 10, 46, 45, 9, 45, 44, 9, 21, 43, 44, 44, 8, 9, 44, 7, 8, 44, 43, 7, 21, 22, 43, 43, 22, 42, 43, 42, 7, 22, 23, 24, 24, 42, 22, 7, 42, 6, 42, 41, 6, 6, 41, 5, 24, 26, 42, 42, 26, 41, 24, 25, 26, 5, 40, 4, 5, 41, 40, 41, 28, 40, 26, 27, 41, 41, 27, 28, 40, 39, 4, 28, 29, 40, 40, 29, 39, 4, 39, 3, 39, 2, 3, 29, 30, 39, 39, 38, 2, 39, 30, 38, 38, 1, 2, 30, 37, 38, 38, 37, 1, 30, 31, 37, 31, 36, 37, 31, 32, 36, 32, 33, 36, 37, 0, 1, 37, 36, 0, 33, 34, 36, 36, 35, 0, 36, 34, 35], "vertices": [1, 44, 53.93999, 69.15, 1, 1, 44, 126.23, 67.30999, 1, 2, 44, 226.41998, 31.12999, 0.9375, 45, -30.87, -1.11, 0.0625, 2, 44, 240.84, 25.32999, 0.69998, 45, -25.63999, 13.52, 0.3, 2, 44, 246.66998, 8.05, 0.3, 45, -8.60999, 20.02, 0.69998, 3, 44, 240.81, -115.25, 0.0625, 45, 114.8, 19.01, 0.875, 46, 9.47999, 59.15999, 0.0625, 2, 45, 131.07, 29.69, 0.69998, 46, 22.11, 44.34999, 0.3, 2, 45, 146.06, 39.54, 0.3, 46, 33.75999, 30.70999, 0.69998, 3, 45, 152.6, 65.01, 0.12566, 46, 59.84999, 27.40999, 0.75203, 47, 15.85, 48.04999, 0.12229, 3, 45, 154.27998, 71.58999, 0.05443, 46, 66.58999, 26.55999, 0.784, 47, 16.71999, 41.31, 0.16154, 3, 46, 71.19, 35.75999, 0.64715, 47, 26.78, 39.16999, 0.13168, 48, -67.31999, 18.95999, 0.22113, 3, 46, 87.93, 69.20999, 0.0625, 47, 63.36999, 31.38999, 0.675, 48, -30.17, 23.29999, 0.26249, 2, 47, 113.81999, 35.72, 0.10379, 48, 16.22999, 43.56, 0.89618, 1, 48, 128.13998, 12.02, 1, 1, 48, 161.85, -15.81, 1, 2, 47, 90.98, -23.36, 0.01379, 48, 13.52, -19.71999, 0.98619, 2, 47, 62.97, -25.80999, 0.69998, 48, -12.22999, -31.02, 0.3, 3, 46, 115.12, -1.33, 0.08332, 47, 1.92999, -12.65999, 0.83332, 48, -74.26, -38.09999, 0.08332, 2, 46, 106.11, -23.53, 0.3, 47, -21.79999, -9.52, 0.69998, 2, 46, 97.43, -44.9, 0.69998, 47, -44.66999, -6.51, 0.3, 2, 46, 84.26, -40.68999, 0.9375, 47, -43.9, 7.28999, 0.0625, 1, 46, 49.18, -29.45999, 1, 2, 45, 206.75, 5.36999, 0.13333, 46, 7.44, -33.77, 0.86665, 2, 45, 219.63998, -20.52, 0.36111, 46, -16.63999, -49.79999, 0.63888, 2, 45, 208.4, -37.81999, 0.72083, 46, -35.22, -40.81999, 0.27915, 2, 45, 200.49, -50.02, 0.91666, 46, -48.31, -34.47999, 0.08332, 1, 45, 161.1, -36.97, 1, 2, 44, 150.1, -116.76, 0.08332, 45, 119.87999, -71.55, 0.91666, 2, 44, 154.99, -70.70999, 0.42846, 45, 73.68, -68.47, 0.57152, 2, 44, 150.3, -65.26999, 0.35604, 45, 68.41999, -73.36, 0.64394, 2, 44, 146.51, -60.86999, 0.59147, 45, 64.16999, -77.31999, 0.40852, 2, 44, 115.12, -75.08, 0.8446, 45, 79.61, -108.12999, 0.15538, 1, 44, 63.33, -98.52999, 1, 1, 44, 21.78, -94.55, 1, 1, 44, -66.69, -32.04, 1, 1, 44, -6.61999, 52.97, 1, 1, 44, 58.13999, -6, 1, 1, 44, 121.16999, 2.44, 1, 1, 44, 188.87, -12.1, 1, 2, 44, 197.11, -18.42, 0.69998, 45, 19.79, -28.44, 0.3, 2, 44, 203.98, -28.61, 0.3, 45, 29.69, -21.17, 0.69998, 1, 45, 136.66998, -7.42, 1, 2, 45, 164.32, 0.66, 0.69998, 46, -2.52999, 7.73, 0.3, 2, 45, 177.97, 21.56999, 0.25, 46, 19.92, -3.19, 0.75, 1, 46, 71.93, -6.28999, 1, 2, 46, 79.66, -3.72, 0.69998, 47, -9.27999, 21.04, 0.3, 2, 46, 87.98, 7.25, 0.3125, 47, 3.42, 15.76, 0.6875, 3, 46, 114.16, 61.84999, 0.03999, 47, 62.84, 4.15, 0.69998, 48, -21.95, -2.66, 0.25999], "edges": [66, 68, 66, 64, 56, 54, 54, 52, 52, 50, 46, 44, 44, 42, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 68, 70, 0, 70, 46, 48, 48, 50, 14, 12, 12, 10, 60, 58, 58, 56, 42, 40, 40, 38, 18, 16, 16, 14, 22, 20, 20, 18, 38, 36, 36, 34, 60, 62, 62, 64, 68, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 16, 88, 86, 88, 18, 90, 90, 38, 88, 90, 20, 92, 92, 36, 90, 92, 92, 94, 94, 22, 94, 32, 30, 24, 88, 40, 86, 14, 84, 12, 82, 10, 82, 52, 48, 84, 44, 86, 78, 6, 4, 76, 80, 8, 80, 56, 58, 78, 76, 60]}}, "raptor_horn": {"raptor_horn": {"x": 156.2, "y": 74.1, "rotation": -137.26, "width": 363, "height": 159}}, "raptor_horn_back": {"raptor_horn_back": {"x": 121.42, "y": 83.01, "rotation": -132.21, "width": 351, "height": 153}}, "raptor_jaw": {"raptor_jaw": {"type": "mesh", "hull": 14, "width": 305, "height": 286, "uvs": [0.40984, 0.22169, 0.42214, 0.3988, 0.67894, 0.53819, 0.7589, 0.62838, 0.99998, 0.4726, 1, 0.53491, 0.87731, 0.77925, 0.63281, 0.94487, 0.39908, 0.96947, 0.19456, 0.89404, 0.21609, 0.6497, 0, 0.46111, 0, 0, 0.26069, 0, 0.19456, 0.29385], "triangles": [14, 13, 0, 14, 0, 1, 5, 3, 4, 10, 14, 1, 11, 14, 10, 6, 3, 5, 2, 10, 1, 7, 2, 3, 7, 3, 6, 2, 8, 10, 8, 9, 10, 2, 7, 8, 14, 12, 13, 11, 12, 14], "vertices": [1, 39, 28.6, 68.84999, 1, 1, 39, 69.65, 38.95, 1, 1, 39, 150.72, 72.87999, 1, 1, 39, 186.16, 74.79, 1, 1, 39, 199.76, 159.69, 1, 1, 39, 213.35, 148.16, 1, 1, 39, 242.43, 74.41999, 1, 1, 39, 230.31, -13.07999, 1, 1, 39, 189.56, -71.98999, 1, 1, 39, 132.76, -105.59999, 1, 1, 39, 83.70999, -55.38999, 1, 2, 8, -18.30999, 12.1, 0.67732, 39, -0.03999, -70.76, 0.32267, 1, 8, 113.44, 16.95, 1, 1, 8, 116.36, -62.47999, 1, 1, 39, 1.86, 5.42999, 1], "edges": [22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 24, 26, 0, 26, 24, 28, 22, 28, 28, 0]}}, "raptor_saddle_noshadow": {"raptor_saddle_noshadow": {"x": 80.83, "y": 10.63, "rotation": -88.64, "width": 326, "height": 375}}, "raptor_saddle_strap_front": {"raptor_saddle_strap_front": {"x": 128.83, "y": -4.71, "rotation": 61.29, "width": 114, "height": 189}}, "raptor_saddle_strap_rear": {"raptor_saddle_strap_rear": {"type": "mesh", "hull": 19, "width": 108, "height": 148, "uvs": [0.85499, 0.06802, 1, 0.13237, 1, 0.20266, 0.95981, 0.26524, 0.88583, 0.38045, 0.80684, 0.46413, 0.74038, 0.53453, 0.81676, 0.5895, 0.51961, 1, 0.4516, 1, 0.01739, 0.8407, 0, 0.80889, 0.24645, 0.36639, 0.3792, 0.39151, 0.42457, 0.32099, 0.49229, 0.21571, 0.57673, 0.10986, 0.66437, 0, 0.70168, 0, 0.56028, 0.46321, 0.68822, 0.29772, 0.76845, 0.18722, 0.61529, 0.39206], "triangles": [7, 8, 6, 9, 10, 13, 13, 11, 12, 6, 8, 19, 8, 9, 19, 9, 13, 19, 13, 10, 11, 19, 22, 6, 13, 14, 19, 19, 14, 22, 6, 22, 5, 22, 20, 5, 5, 20, 4, 14, 15, 22, 22, 15, 20, 20, 21, 4, 15, 16, 20, 4, 21, 3, 20, 16, 21, 2, 3, 0, 3, 21, 0, 0, 1, 2, 21, 16, 18, 16, 17, 18, 21, 18, 0], "vertices": [1, 52, 3.9, -3.26999, 1, 1, 52, 4.25, 15.05, 1, 1, 52, 13.23999, 20.28, 1, 2, 52, 23.42, 21.2, 0.69998, 53, -15.19999, 21.21999, 0.3, 3, 52, 41.11, 22.87, 0.3, 53, 2.48, 22.88999, 0.63748, 54, -33.83, 24.95999, 0.0625, 3, 52, 52.06999, 21.71999, 0.0625, 53, 13.43, 21.73999, 0.63748, 54, -22.96999, 23.11, 0.3, 2, 53, 18.38999, 20.76, 0.25, 54, -18.09, 21.81999, 0.75, 1, 54, -18.76, 33.09, 1, 1, 54, 49.91999, 31.56999, 1, 1, 54, 53.20999, 25, 1, 1, 54, 53.11, -27.47999, 1, 1, 54, 49.74, -31.27, 1, 1, 54, -20.72999, -36.75999, 1, 1, 54, -23.81999, -22.28, 1, 3, 52, 53.47999, -24.61, 0.0625, 53, 14.84, -24.59, 0.57498, 54, -24.51, -23.20999, 0.3625, 3, 52, 41.43999, -26.12, 0.3, 53, 2.80999, -26.09, 0.63748, 54, -36.61999, -23.95, 0.0625, 2, 52, 24.37999, -26.12, 0.69998, 53, -14.23999, -26.1, 0.3, 1, 52, 5.57, -26.12, 1, 1, 52, 3.53999, -22.63999, 1, 1, 54, -23.07999, -0.03999, 1, 2, 52, 41.65999, -1.72, 0.3125, 53, 3.02999, -1.7, 0.6875, 2, 52, 23.85, -2.46, 0.69998, 53, -14.77, -2.44, 0.3, 2, 53, 13.94999, -1.5, 0.64582, 54, -23.94, -0.10999, 0.35416], "edges": [26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 4, 2, 34, 36, 12, 38, 38, 26, 8, 40, 40, 30, 2, 0, 0, 36, 30, 32, 32, 34, 32, 42, 4, 6, 6, 8, 42, 6, 26, 28, 28, 30, 28, 44, 8, 10, 10, 12, 44, 10]}}, "raptor_saddle_w_shadow": {"raptor_saddle_w_shadow": {"x": 80.83, "y": 10.63, "rotation": -88.64, "width": 326, "height": 375}}, "raptor_tongue": {"raptor_tongue": {"type": "mesh", "hull": 14, "width": 171, "height": 128, "uvs": [0.35242, 0.2156, 0.4794, 0.44245, 0.62071, 0.61176, 0.80562, 0.75373, 1, 0.90297, 1, 1, 0.8971, 1, 0.72054, 0.92254, 0.50668, 0.82872, 0.30401, 0.70725, 0.10537, 0.57888, 0, 0.50622, 0, 0, 0.26224, 0], "triangles": [8, 7, 6, 6, 4, 5, 4, 6, 3, 6, 7, 3, 7, 8, 3, 8, 2, 3, 9, 10, 1, 8, 9, 2, 9, 1, 2, 1, 10, 0, 10, 11, 0, 0, 12, 13, 0, 11, 12], "vertices": [2, 63, 3.63, 27.04, 0.6875, 64, -47.25999, 33.86999, 0.3125, 3, 63, 39.09, 19.45, 0.3125, 64, -13.40999, 20.86, 0.625, 65, -51.54, 33.36999, 0.0625, 3, 63, 71.55999, 19.02, 0.0625, 64, 18.57999, 15.39, 0.625, 65, -21.55999, 20.92, 0.3125, 2, 64, 55.02999, 16.85, 0.3125, 65, 14.28999, 14.22999, 0.6875, 2, 64, 93.33999, 18.38999, 0.08332, 65, 51.97999, 7.21, 0.91666, 1, 65, 56.09, -4.5, 1, 2, 64, 85.05999, -1.49, 0.08332, 65, 39.47999, -10.32999, 0.91666, 2, 64, 54.22, -9.18, 0.3125, 65, 7.71, -10.96, 0.6875, 3, 63, 75.13999, -14.72, 0.0625, 64, 16.87, -18.5, 0.625, 65, -30.77, -11.72999, 0.3125, 3, 63, 38.79999, -25.79999, 0.3125, 64, -20.73999, -23.79999, 0.625, 65, -68.62, -8.52999, 0.0625, 2, 63, 2.4, -35.77, 0.6875, 64, -58.25, -27.98999, 0.3125, 2, 63, -17.28, -40.61999, 0.91666, 64, -78.44999, -29.70999, 0.08332, 1, 63, -59.90999, 8.18, 1, 2, 63, -26.12999, 37.68999, 0.91666, 64, -75.01999, 49.02, 0.08332], "edges": [22, 24, 10, 12, 10, 8, 24, 26, 16, 4, 18, 16, 2, 4, 18, 2, 22, 20, 0, 26, 20, 0, 0, 2, 12, 14, 14, 16, 4, 6, 6, 8, 14, 6, 20, 18]}}, "spineboy_torso": {"torso": {"x": 55.88, "y": 4.86, "rotation": -104.14, "width": 108, "height": 182}}, "stirrup_back": {"stirrup_back": {"x": 53.2, "y": 31.34, "rotation": -21.12, "width": 87, "height": 69}}, "stirrup_front": {"stirrup_front": {"x": 36.13, "y": 20.39, "rotation": -21.12, "width": 89, "height": 100}}, "stirrup_strap": {"stirrup_strap": {"type": "mesh", "hull": 16, "width": 97, "height": 91, "uvs": [0.36822, 0.27893, 0.45737, 0.38897, 0.54451, 0.49651, 0.67872, 0.59135, 0.81977, 0.69102, 1, 0.77344, 1, 1, 0.77956, 1, 0.63729, 0.81629, 0.53364, 0.72348, 0.40534, 0.6086, 0.30886, 0.52535, 0.21049, 0.44047, 0, 0.26245, 0, 0, 0.30637, 0, 0.20241, 0.23], "triangles": [2, 10, 1, 9, 10, 2, 9, 2, 3, 8, 9, 3, 8, 3, 4, 7, 8, 4, 7, 4, 5, 7, 5, 6, 16, 14, 15, 13, 14, 16, 16, 15, 0, 12, 16, 0, 12, 0, 1, 13, 16, 12, 11, 12, 1, 10, 11, 1], "vertices": [2, 56, 24.70999, 8.02999, 0.80343, 57, -17.42, 11.02, 0.19654, 2, 56, 37.95, 8.03999, 0.59978, 57, -4.36, 8.86999, 0.4002, 2, 56, 50.88, 8.03999, 0.36895, 57, 8.39, 6.76999, 0.63103, 2, 56, 65.91999, 12.27, 0.17747, 57, 23.90999, 8.47999, 0.82251, 2, 56, 81.72, 16.7, 0.05942, 57, 40.22999, 10.27999, 0.94055, 2, 56, 98.81999, 25.04, 0.01209, 57, 58.45999, 15.71, 0.9879, 2, 56, 114.44, 11.56999, 0.0019, 57, 71.66999, -0.10999, 0.99808, 2, 56, 100.47, -4.61, 0.01816, 57, 55.25, -13.81, 0.98181, 2, 56, 78.79, -4.13999, 0.07486, 57, 33.93999, -9.81, 0.92511, 2, 56, 65.83, -6.23999, 0.2028, 57, 20.80999, -9.76, 0.79719, 2, 56, 49.77999, -8.82999, 0.3997, 57, 4.55, -9.69999, 0.60027, 2, 56, 37.93, -10.97, 0.62657, 57, -7.48, -9.88, 0.3734, 2, 56, 25.85, -13.14999, 0.82033, 57, -19.75, -10.06, 0.17964, 2, 56, 0.25, -18.03, 0.95288, 57, -45.81, -10.69999, 0.0471, 2, 56, -17.82999, -2.43, 0.97709, 57, -61.11, 7.63, 0.0229, 2, 56, 1.57, 20.06999, 0.94774, 57, -38.29, 26.67, 0.05225, 2, 56, 10.84, -1.23, 0.97709, 57, -32.61999, 4.13999, 0.0229], "edges": [28, 30, 30, 0, 12, 10, 8, 10, 12, 14, 14, 16, 26, 28, 24, 26, 26, 32, 32, 30, 20, 22, 22, 24, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20]}}, "visor": {"visor": {"x": 99.13, "y": 6.51, "rotation": -70.57, "width": 261, "height": 168}}}}, "animations": {"Jump": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": [0.201, 0.17, 0.815, 0.83]}, {"time": 0.6666, "x": 1482.78, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "front_foot_goal": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}, {"time": 0.3, "angle": -41.64}, {"time": 0.3666, "angle": -69.66}, {"time": 0.4333, "angle": -12.8}, {"time": 0.5333, "angle": 5.73}, {"time": 0.6666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.3666, "x": -60.01, "y": 111.1}, {"time": 0.4333, "x": 213.18, "y": 291.22}, {"time": 0.5333, "x": 243.73, "y": 332.61}, {"time": 0.6666, "x": 95.94, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6666, "x": 1, "y": 1}]}, "hip": {"rotate": [{"time": 0, "angle": -4.48}, {"time": 0.1, "angle": -23.02}, {"time": 0.3, "angle": 19.24}, {"time": 0.5333, "angle": 20.85}, {"time": 0.6666, "angle": -10.76}, {"time": 0.7666, "angle": -18.58}, {"time": 0.9333, "angle": -3.56}, {"time": 1.0666, "angle": -4.48}], "translate": [{"time": 0, "x": -100.65, "y": 49.77}, {"time": 0.1, "x": 9.37, "y": -109.06, "curve": [0.245, 0, 0.609, 0.41]}, {"time": 0.1666, "x": 150.37, "y": -76.51, "curve": [0.401, 0.34, 0.858, 0.87]}, {"time": 0.3, "x": 361, "y": 36.69}, {"time": 0.5333, "x": 5.36, "y": 290.91, "curve": [0.808, 0, 0.892, 0.81]}, {"time": 0.6666, "x": -56.27, "y": 88.07}, {"time": 0.7666, "x": 179.93, "y": -59.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 238.11, "y": 50.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0666, "x": 213.19, "y": 49.77}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_foot_goal": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}, {"time": 0.3, "angle": -41.64}, {"time": 0.3666, "angle": -69.66}, {"time": 0.4333, "angle": -57.97}, {"time": 0.6999, "angle": -9.19}, {"time": 0.7333, "angle": -7.78}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.3666, "x": -131.66, "y": 47.58}, {"time": 0.4333, "x": -16.1, "y": 205.84}, {"time": 0.5333, "x": 61.29, "y": 320.2}, {"time": 0.7333, "x": 235.62, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6999, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.7333, "x": 1, "y": 1}]}, "front_leg1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_leg_goal": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6999, "x": -0.39, "y": 24.29}, {"time": 0.7666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "rear_leg1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_leg_goal": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6999, "x": 6.75, "y": 25.64}, {"time": 0.7666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "tail1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": -11.02}, {"time": 0.3, "angle": 0.53}, {"time": 0.4333, "angle": 8.64}, {"time": 0.6999, "angle": -9.73}, {"time": 0.7666, "angle": -4.46, "curve": [0.243, 0, 0.648, 1]}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "torso1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_leg2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_leg2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "saddle": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tail2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": -39.83}, {"time": 0.3, "angle": -31.82}, {"time": 0.4333, "angle": -7.28}, {"time": 0.5333, "angle": 1.28}, {"time": 0.6, "angle": -7.22}, {"time": 0.6999, "angle": -30.66}, {"time": 0.7666, "angle": -40.54, "curve": [0.243, 0, 0.648, 1]}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "torso2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 0.51}, {"time": 0.3, "angle": -1.9}, {"time": 0.5333, "angle": 1.04}, {"time": 0.6999, "angle": -3.25}, {"time": 0.7666, "angle": 4.81}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_arm1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1666, "angle": -308.79}, {"time": 0.3, "angle": -398.7}, {"time": 0.5333, "angle": -297.8}, {"time": 0.6999, "angle": 62.19}, {"time": 0.7666, "angle": -325.36}, {"time": 0.8333, "angle": -374.42}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_leg3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "neck": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": -8.25}, {"time": 0.3, "angle": -1.9}, {"time": 0.5333, "angle": 5.44}, {"time": 0.6999, "angle": 24.01}, {"time": 0.7666, "angle": 4.82}, {"time": 0.8666, "angle": -1.78}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.3, "x": 62.9, "y": -44.25}, {"time": 0.5333, "x": -4.35, "y": 17.31}, {"time": 0.6999, "x": 0, "y": 0}, {"time": 0.7666, "x": 23.29, "y": -42.27}, {"time": 0.8666, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_arm1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 51.2}, {"time": 0.3333, "angle": -38.7}, {"time": 0.6, "angle": 62.19, "curve": "stepped"}, {"time": 0.7333, "angle": 62.19}, {"time": 0.8, "angle": 34.62}, {"time": 0.8666, "angle": -14.43}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_leg3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "saddle_strap_front1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "saddle_strap_rear1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "spineboy_front_arm_goal": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "spineboy_hip": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8666, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 35.96, "y": -11.83}, {"time": 0.3, "x": 31.1, "y": -50.39}, {"time": 0.5333, "x": 12.1, "y": -8.03}, {"time": 0.6999, "x": 41.7, "y": -19.46}, {"time": 0.8666, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8666, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "spineboy_rear_arm_goal": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "stirrup": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "stirrup_strap1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tail3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": -8.97}, {"time": 0.3, "angle": -18.38}, {"time": 0.4333, "angle": 0.9}, {"time": 0.5333, "angle": 11.43}, {"time": 0.6, "angle": 17.22}, {"time": 0.6999, "angle": 4.74}, {"time": 0.7666, "angle": -20.69, "curve": [0.243, 0, 0.648, 1]}, {"time": 0.9666, "angle": -20.4, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "back_thigh": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_arm2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 23.1}, {"time": 0.3, "angle": -75.92}, {"time": 0.5333, "angle": -1.41}, {"time": 0.7666, "angle": 26.86}, {"time": 0.8333, "angle": -56.14}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_foot1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_thigh": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "gun": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 15.27}, {"time": 0.3, "angle": -53.4}, {"time": 0.5666, "angle": -63.35}, {"time": 0.7666, "angle": -29.92}, {"time": 0.8999, "angle": 7.24}, {"time": 1, "angle": -3.69}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "head": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 9.93}, {"time": 0.3, "angle": -3.76}, {"time": 0.5333, "angle": -26.63}, {"time": 0.6999, "angle": -10.23}, {"time": 0.7666, "angle": 21.8}, {"time": 0.8666, "angle": 15.36}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_arm2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 23.1}, {"time": 0.3, "angle": -75.92}, {"time": 0.5333, "angle": -1.41}, {"time": 0.7666, "angle": 26.86}, {"time": 0.8333, "angle": -56.14}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_foot1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "saddle_strap_front2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "saddle_strap_rear2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "spineboy_torso": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1666, "angle": -24.93}, {"time": 0.2333, "angle": -20.34}, {"time": 0.5333, "angle": -11.2}, {"time": 0.6999, "angle": 10.49}, {"time": 0.8333, "angle": -30.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.34}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "stirrup_strap2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tail4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 34.12}, {"time": 0.3, "angle": -12.25}, {"time": 0.4333, "angle": 11.11}, {"time": 0.5333, "angle": 25.19}, {"time": 0.6, "angle": 32.5}, {"time": 0.6999, "angle": 24.4}, {"time": 0.7666, "angle": 9.9, "curve": [0.243, 0, 0.648, 1]}, {"time": 0.9666, "angle": -11.72, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "back_arm": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "back_knee": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_arm": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_foot2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0}, {"time": 0.3666, "angle": -63.6}, {"time": 0.4333, "angle": -80.16}, {"time": 0.5333, "angle": -17.48}, {"time": 0.6666, "angle": 24.85}, {"time": 0.7666, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6666, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6666, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_hand": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5333, "angle": -27.74}, {"time": 0.7666, "angle": -27.09}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "horn_front": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "horn_rear": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "jaw": {"rotate": [{"time": 0, "angle": 15.56}, {"time": 0.2333, "angle": -0.92}, {"time": 0.5, "angle": 20.4}, {"time": 0.6999, "angle": 18.32}, {"time": 0.7666, "angle": 5.17}, {"time": 0.8333, "angle": 20.34}, {"time": 1.0666, "angle": 15.56}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "lower_leg": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "neck2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 11.08}, {"time": 0.8333, "angle": 8.16}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_foot2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0}, {"time": 0.3666, "angle": -87.93}, {"time": 0.4333, "angle": -126.75}, {"time": 0.5333, "angle": -63.79}, {"time": 0.6999, "angle": 24.85}, {"time": 0.7666, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6999, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6999, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_hand": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5333, "angle": -27.74}, {"time": 0.7666, "angle": -27.09}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "saddle_strap_rear3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tail5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 76.87}, {"time": 0.3, "angle": -12.25}, {"time": 0.4333, "angle": 10.5}, {"time": 0.5333, "angle": 24.81}, {"time": 0.6, "angle": 32.21}, {"time": 0.6999, "angle": 24.4}, {"time": 0.7666, "angle": 9.9, "curve": [0.243, 0, 0.648, 1]}, {"time": 0.9666, "angle": -41.66, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tongue1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "back_bracer": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_arm_target": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_bracer": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_foot3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0}, {"time": 0.3666, "angle": -84.17}, {"time": 0.4333, "angle": -127.53}, {"time": 0.5333, "angle": -52.16}, {"time": 0.6666, "angle": 10.77}, {"time": 0.7666, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6666, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6666, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "head2": {"rotate": [{"time": 0, "angle": 15.31}, {"time": 0.1, "angle": 29.85}, {"time": 0.2, "angle": 22.43}, {"time": 0.3, "angle": 12.64}, {"time": 0.4666, "angle": 24.85}, {"time": 0.5333, "angle": 9.28}, {"time": 0.6999, "angle": 4.77}, {"time": 0.7666, "angle": 37.9}, {"time": 0.8333, "angle": 18.87, "curve": [0.056, 0.81, 0.75, 1]}, {"time": 1, "angle": 22.96}, {"time": 1.0666, "angle": 15.31}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "rear_arm_target": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tongue2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "back_hand": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_hand2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "tongue3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}}}, "empty": {}, "gungrab": {"slots": {"front_hand": {"attachment": [{"time": 0, "name": "front_open_hand"}, {"time": 0.1666, "name": "gun"}]}, "gun": {"attachment": [{"time": 0, "name": "gun_nohand"}, {"time": 0.1666, "name": null}]}}, "bones": {"front_hand2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": 12.33}, {"time": 0.1666, "angle": -89.54}, {"time": 0.2333, "angle": -79.78}, {"time": 0.4666, "angle": -10.18}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1666, "x": 0.938, "y": 0.938}, {"time": 0.4666, "x": 1, "y": 1}]}, "front_arm": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.0666, "angle": -31.99}, {"time": 0.1666, "angle": 223.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 155.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3666, "angle": 246.13, "curve": [0.184, 0.33, 0.75, 1]}, {"time": 0.4666, "angle": -56.74}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1666, "x": 6.49, "y": -2.66}, {"time": 0.2333, "x": 6.84, "y": 4.79}, {"time": 0.4666, "x": 0, "y": 0}]}, "front_bracer": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1666, "angle": 86.01}, {"time": 0.2333, "angle": 114.94}, {"time": 0.3666, "angle": 81.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4666, "angle": 34.73}]}}, "ik": {"front_arm_goal": [{"time": 0, "mix": 0}]}}, "walk": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_foot_goal": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2666, "angle": -51.26}, {"time": 0.4, "angle": -65.17}, {"time": 0.5333, "angle": -76.28}, {"time": 0.8, "angle": -76.52}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 343.28, "y": 36.5}, {"time": 0.2666, "x": 86.5, "y": 36.99}, {"time": 0.5333, "x": -173.36, "y": 37.42}, {"time": 0.6, "x": -68.15, "y": 141.15}, {"time": 0.7333, "x": 91.78, "y": 238.01}, {"time": 0.8, "x": 155.89, "y": 190.91}, {"time": 0.9666, "x": 303.28, "y": 94.4}, {"time": 1.0666, "x": 343.28, "y": 36.5}]}, "hip": {"rotate": [{"time": 0, "angle": -4.78}, {"time": 0.0666, "angle": -3.99}, {"time": 0.2666, "angle": -12.49}, {"time": 0.5333, "angle": -4.78}, {"time": 0.6, "angle": -3.99}, {"time": 0.8, "angle": -12.49}, {"time": 1.0666, "angle": -4.78}], "translate": [{"time": 0, "x": 161.93, "y": 4.89, "curve": [0.27, 0.37, 0.62, 0.4]}, {"time": 0.0666, "x": 165.04, "y": -5.99, "curve": [0.244, 0, 0.757, 1]}, {"time": 0.2666, "x": 178.8, "y": 136.52, "curve": [0.25, 0, 0.841, 0.8]}, {"time": 0.5333, "x": 161.93, "y": 4.89, "curve": [0.27, 0.37, 0.62, 0.4]}, {"time": 0.6, "x": 165.04, "y": -5.99, "curve": [0.244, 0, 0.757, 1]}, {"time": 0.8, "x": 178.8, "y": 136.52, "curve": [0.25, 0, 0.858, 0.81]}, {"time": 1.0666, "x": 161.93, "y": 4.89}]}, "rear_foot_goal": {"rotate": [{"time": 0, "angle": -62.73}, {"time": 0.2666, "angle": -107.17}, {"time": 0.4666, "angle": -40.51}, {"time": 0.8, "angle": -97.15}, {"time": 1.0666, "angle": -62.73}], "translate": [{"time": 0, "x": -266.69, "y": -15.46}, {"time": 0.1333, "x": -87.88, "y": 124.85}, {"time": 0.2666, "x": 88.35, "y": 134.06}, {"time": 0.3666, "x": 198.39, "y": 90.64}, {"time": 0.4666, "x": 308.19, "y": -26.42}, {"time": 0.6, "x": 167.06, "y": -26.42}, {"time": 1.0666, "x": -266.69, "y": -15.46}]}, "front_leg1": {"rotate": [{"time": 0, "angle": 27.07}, {"time": 1.0666, "angle": 31.39}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.0666, "x": -0.21, "y": 15.19}, {"time": 0.5333, "x": -0.33, "y": 12.15}, {"time": 0.7333, "x": -4.74, "y": 31.93}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_leg_goal": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": -18.04, "y": -2.88}, {"time": 0.4333, "x": -42.2, "y": -88.63}, {"time": 0.5333, "x": -27.31, "y": -43.9}, {"time": 0.7333, "x": -1.52, "y": -94.28}, {"time": 0.8, "x": -24.29, "y": -116.41}, {"time": 1, "x": -41.88, "y": -93.3}, {"time": 1.0666, "x": -18.04, "y": -2.88}]}, "rear_leg1": {"rotate": [{"time": 0, "angle": -64.85}, {"time": 1.0666, "angle": -45.71}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "rear_leg_goal": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": -2.05, "y": 15.12}, {"time": 0.2666, "x": 17.49, "y": -150.43}, {"time": 0.4666, "x": -40.21, "y": -81.76}, {"time": 0.5333, "x": -31.68, "y": -82.43}, {"time": 0.8, "x": 2.65, "y": -169.21}, {"time": 0.9333, "x": -16.76, "y": -98.31}, {"time": 1.0666, "x": -2.05, "y": 15.12}]}, "tail1": {"rotate": [{"time": 0, "angle": 1.3}, {"time": 0.0666, "angle": 4.13}, {"time": 0.3333, "angle": -5.77}, {"time": 0.6333, "angle": 4.13}, {"time": 0.8999, "angle": -5.77}, {"time": 1.0666, "angle": 1.3}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0666, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "torso1": {"rotate": [{"time": 0, "angle": 7.21}, {"time": 0.2666, "angle": 4.19}, {"time": 0.5333, "angle": 7.21}, {"time": 0.8, "angle": 4.19}, {"time": 1.0666, "angle": 7.21}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_leg2": {"rotate": [{"time": 0, "angle": -347.28}, {"time": 1.0666, "angle": -362.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "rear_leg2": {"rotate": [{"time": 0, "angle": 27.05}, {"time": 1.0666, "angle": 9.92}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "saddle": {"rotate": [{"time": 0, "angle": -2.51}, {"time": 0.2666, "angle": -4.17}, {"time": 0.5333, "angle": -3.85}, {"time": 0.8, "angle": -3.09}, {"time": 1.0666, "angle": -2.51}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2666, "x": 0, "y": 0, "curve": [0.149, 0.28, 0.75, 1]}, {"time": 0.3333, "x": -0.03, "y": 5.91, "curve": [0.421, 0, 0.85, 0.78]}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6, "x": -0.2, "y": -2.35}, {"time": 0.8, "x": 0, "y": 0, "curve": [0.149, 0.28, 0.75, 1]}, {"time": 0.8666, "x": -0.03, "y": 5.91, "curve": [0.421, 0, 0.85, 0.78]}, {"time": 1.0666, "x": 0, "y": 0}]}, "tail2": {"rotate": [{"time": 0, "angle": -19.15}, {"time": 0.2333, "angle": -11.3}, {"time": 0.5, "angle": -9.37}, {"time": 0.7666, "angle": -11.3}, {"time": 1.0333, "angle": -20.27}, {"time": 1.0666, "angle": -19.15}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.8, "y": 1}, {"time": 0.2333, "x": 0.899, "y": 1}, {"time": 0.5, "x": 0.8, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 0.8, "y": 1}]}, "torso2": {"rotate": [{"time": 0, "angle": 8.6}, {"time": 0.2666, "angle": 9.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 8.01}, {"time": 0.8, "angle": 5.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0666, "angle": 8.6}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_arm1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -367.82}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 20.64, "y": -7.55}, {"time": 0.5, "x": -2.86, "y": 3.32}, {"time": 0.8, "x": 24.09, "y": -1.47}, {"time": 0.9333, "x": 21.73, "y": -3.7}, {"time": 1.0666, "x": 20.64, "y": -7.55}]}, "front_leg3": {"rotate": [{"time": 0, "angle": 1.14, "curve": "stepped"}, {"time": 1.0666, "angle": 1.14}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "neck": {"rotate": [{"time": 0, "angle": 6.5}, {"time": 0.2666, "angle": 12.71}, {"time": 0.5333, "angle": 6.5}, {"time": 0.8, "angle": 12.71}, {"time": 1.0666, "angle": 6.5}], "translate": [{"time": 0, "x": 12.59, "y": -31.3}, {"time": 0.2666, "x": -10.84, "y": -72.28, "curve": [0.204, 0.01, 0.861, 0.86]}, {"time": 0.5333, "x": 12.59, "y": -31.3}, {"time": 0.8, "x": -10.84, "y": -72.28, "curve": [0.204, 0.01, 0.861, 0.86]}, {"time": 1.0666, "x": 12.59, "y": -31.3}]}, "rear_arm1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 13.71}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 11.12, "y": -13.38}, {"time": 1.0666, "x": 0, "y": 0}]}, "rear_leg3": {"rotate": [{"time": 0, "angle": -23.18, "curve": "stepped"}, {"time": 1.0666, "angle": -23.18}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "saddle_strap_front1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "saddle_strap_rear1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "tail3": {"rotate": [{"time": 0, "angle": -12.46}, {"time": 0.2333, "angle": 12.65}, {"time": 0.5, "angle": -20.79}, {"time": 0.7666, "angle": 12.65}, {"time": 1.0333, "angle": -16.04}, {"time": 1.0666, "angle": -12.46}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 1}, {"time": 0.5, "x": 0.997, "y": 1}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_arm2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 22.44}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_foot1": {"rotate": [{"time": 0, "angle": -41.33, "curve": "stepped"}, {"time": 1.0666, "angle": -41.33}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": -7.36}, {"time": 0.1333, "angle": -12.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -6.12}, {"time": 0.5333, "angle": -7.36}, {"time": 0.6666, "angle": -12.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -6.12}, {"time": 1.0666, "angle": -7.36}], "translate": [{"time": 0, "x": -3.88, "y": -32.87}, {"time": 0.9333, "x": -3.33, "y": -22.81}, {"time": 1.0666, "x": -3.88, "y": -32.87}]}, "rear_arm2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -30.2}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "rear_foot1": {"rotate": [{"time": 0, "angle": 2.07, "curve": "stepped"}, {"time": 1.0666, "angle": 2.07}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "saddle_strap_front2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "saddle_strap_rear2": {"rotate": [{"time": 0, "angle": -4.44}, {"time": 0.1, "angle": -2.66}, {"time": 0.3, "angle": -0.35}, {"time": 0.4333, "angle": -1.7}, {"time": 0.6333, "angle": -2.54}, {"time": 0.8999, "angle": -0.51}, {"time": 1.0666, "angle": -4.44}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "stirrup": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2666, "angle": -4.95}, {"time": 0.5333, "angle": 0}, {"time": 0.8, "angle": -4.95}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 8.97, "y": 4.99}, {"time": 0.2666, "x": 4.85, "y": 0.99}, {"time": 0.5333, "x": 7.75, "y": -2.99}, {"time": 0.8, "x": 4.85, "y": 0.99}, {"time": 1.0666, "x": 8.97, "y": 4.99}]}, "tail4": {"rotate": [{"time": 0, "angle": 10.25}, {"time": 0.2333, "angle": 39.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 1.33}, {"time": 0.7666, "angle": 39.47, "curve": [0.664, 0, 0.75, 1]}, {"time": 1.0333, "angle": 6.08}, {"time": 1.0666, "angle": 10.25}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0666, "x": 1, "y": 1}]}, "front_foot2": {"rotate": [{"time": 0, "angle": 36.9}, {"time": 0.0666, "angle": 7.88}, {"time": 0.1333, "angle": 4.66}, {"time": 0.4, "angle": 7.59}, {"time": 0.5333, "angle": 8.08}, {"time": 0.6666, "angle": -67.33}, {"time": 0.7333, "angle": -65.23}, {"time": 1, "angle": 27.74}, {"time": 1.0666, "angle": 36.9}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_hand": {"rotate": [{"time": 0, "angle": 9.49}, {"time": 0.5, "angle": -48.6}, {"time": 1.0666, "angle": 9.49}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "horn_front": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2666, "x": -7.18, "y": -1.38}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8, "x": -7.18, "y": -1.38}, {"time": 1.0666, "x": 0, "y": 0}]}, "horn_rear": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2666, "x": 12.34, "y": 9.16}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8, "x": 12.34, "y": 9.16}, {"time": 1.0666, "x": 0, "y": 0}]}, "jaw": {"rotate": [{"time": 0, "angle": 25.56}, {"time": 0.2, "angle": 21.27}, {"time": 0.3333, "angle": 21.35}, {"time": 0.6666, "angle": 15.6}, {"time": 0.8666, "angle": 22.96}, {"time": 1.0666, "angle": 25.56}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "rear_foot2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": -82.37}, {"time": 0.2666, "angle": -110.3}, {"time": 0.4333, "angle": 36.21}, {"time": 0.5333, "angle": 2.1}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "rear_hand": {"rotate": [{"time": 0, "angle": -28.89}, {"time": 0.5, "angle": 12.19}, {"time": 1.0666, "angle": -28.89}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "saddle_strap_rear3": {"rotate": [{"time": 0, "angle": -1.31}, {"time": 0.1, "angle": 0.46}, {"time": 0.3, "angle": 2.77}, {"time": 0.4333, "angle": 1.42}, {"time": 0.6333, "angle": 0.58}, {"time": 0.8999, "angle": 2.61}, {"time": 1.0666, "angle": -1.31}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "tail5": {"rotate": [{"time": 0, "angle": -26.34, "curve": [0.391, -0.58, 0.653, 1.01]}, {"time": 0.2333, "angle": 45.41, "curve": [0.391, -0.58, 0.653, 1.01]}, {"time": 0.5, "angle": -21.92, "curve": [0.391, -0.58, 0.653, 1.01]}, {"time": 0.7666, "angle": 45.41, "curve": [0.391, -0.58, 0.653, 1.01]}, {"time": 1.0333, "angle": -7.73}, {"time": 1.0666, "angle": -26.34}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.765, "y": 1}, {"time": 0.2333, "x": 1, "y": 1}, {"time": 0.5, "x": 0.765, "y": 1}, {"time": 0.7666, "x": 1, "y": 1}, {"time": 1.0666, "x": 0.765, "y": 1}]}, "tongue1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 7.55}, {"time": 0.6666, "angle": -1.68}, {"time": 0.9333, "angle": 8.11}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "front_foot3": {"rotate": [{"time": 0, "angle": -1.65}, {"time": 0.0666, "angle": -5.29}, {"time": 0.1333, "angle": -3.94}, {"time": 0.2666, "angle": -3.81}, {"time": 0.5333, "angle": -5.89}, {"time": 0.6, "angle": -21.2}, {"time": 0.6666, "angle": -73.63}, {"time": 0.7333, "angle": -102.81}, {"time": 0.8333, "angle": -41.3}, {"time": 1, "angle": 10.93}, {"time": 1.0666, "angle": -1.65}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "tongue2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 7.55}, {"time": 0.6666, "angle": -1.68}, {"time": 0.9333, "angle": 8.11}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "tongue3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 7.55}, {"time": 0.6666, "angle": -1.68}, {"time": 0.9333, "angle": 8.11}, {"time": 1.0666, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0666, "x": 0, "y": 0}]}, "head2": {"rotate": [{"time": 0, "angle": 38.59}, {"time": 0.2666, "angle": 43.18}, {"time": 0.5333, "angle": 38.59}, {"time": 0.8, "angle": 43.18}, {"time": 1.0666, "angle": 38.59}]}, "neck2": {"rotate": [{"time": 0, "angle": 9.65}, {"time": 0.2666, "angle": 14.71}, {"time": 0.5333, "angle": 9.65}, {"time": 0.8, "angle": 14.71}, {"time": 1.0666, "angle": 9.65}]}, "spineboy_hip": {"translate": [{"time": 0, "x": 32.54, "y": 1.34, "curve": [0.412, 0, 0.872, 0.78]}, {"time": 0.2666, "x": -12.88, "y": 0.58, "curve": [0.138, 0.17, 0.75, 1]}, {"time": 0.5333, "x": 32.54, "y": 1.34, "curve": [0.367, 0, 0.867, 0.81]}, {"time": 0.8, "x": -12.88, "y": 0.58, "curve": [0.164, 0.17, 0.75, 1]}, {"time": 1.0666, "x": 32.54, "y": 1.34}]}, "spineboy_torso": {"rotate": [{"time": 0, "angle": -37.93}, {"time": 0.2666, "angle": -29.47, "curve": [0.492, 0, 0.75, 1]}, {"time": 0.5333, "angle": -37.93}, {"time": 0.8, "angle": -29.47, "curve": [0.492, 0, 0.75, 1]}, {"time": 1.0666, "angle": -37.71}]}, "front_arm": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -14.43, "y": -11.03}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8, "x": -14.43, "y": -11.03}, {"time": 1.0666, "x": 0, "y": 0}]}, "gun": {"rotate": [{"time": 0, "angle": -11.68, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.0666, "angle": -17.59}, {"time": 0.3333, "angle": 14.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -24.74, "curve": [0.326, 0, 0.715, 1]}, {"time": 0.8666, "angle": 14.45, "curve": [0.242, 0, 0.666, 0.66]}, {"time": 1.0666, "angle": -11.68}], "translate": [{"time": 0, "x": 0.84, "y": -3.81}, {"time": 0.0666, "x": 0, "y": 0}, {"time": 0.3333, "x": 3.37, "y": -15.27}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.8666, "x": 3.37, "y": -15.27}, {"time": 1.0666, "x": 0.84, "y": -3.81}]}}, "deform": {"default": {"raptor_body": {"raptor_body": [{"time": 0}, {"time": 0.2666, "offset": 314, "vertices": [-16.78684, 15.47479, -0.63024, 22.82083, 18.11511, 13.89254, 19.32452, 12.15423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, 0, 0, 0, 0, 0, 0, -3.24548, 0.81152, 0, 0, -3.24548, 0.81152]}, {"time": 0.5333}, {"time": 0.8, "offset": 314, "vertices": [-16.78684, 15.47479, -0.63024, 22.82083, 18.11511, 13.89254, 19.32452, 12.15423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, -3.24548, 0.81152, 0, 0, 0, 0, 0, 0, -3.24548, 0.81152, 0, 0, -3.24548, 0.81152]}, {"time": 1.0666}]}, "raptor_front_leg": {"raptor_front_leg": [{"time": 0, "curve": "stepped"}, {"time": 0.2666}, {"time": 0.5333, "offset": 138, "vertices": [-2.23608, 21.95403, 21.54915, -4.7554]}, {"time": 0.6, "offset": 138, "vertices": [7.17962, 15.14358, 15.26523, -6.91741]}, {"time": 0.7333, "offset": 110, "vertices": [-0.82485, 0.73406, -0.01284, -1.10443, 0, 0, 0, 0, 1.4866, -2.59426, 0.98071, 2.82342, 2.7366, -10.49935, 6.12506, 8.95281, -2.60873, -2.28384, 3.43417, -0.47045, -2.28305, -4.76037, 5.08892, 1.40078, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.37966, -1.75515, -2.13229, -0.65667, -2.86239, -0.77365, 2.45989, -1.65319]}, {"time": 0.8, "curve": "stepped"}, {"time": 0.9666, "curve": "stepped"}, {"time": 1.0666}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]