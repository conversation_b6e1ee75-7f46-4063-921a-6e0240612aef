[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "324247e8-c584-495d-87b3-015a69fee444"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 5}, {"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 320, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_fitWidth": false, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29zXboiXFBKoIV4PQ2liTe"}]